#!/usr/bin/env python3
"""
快速优化脚本 - 专门针对速度预测任务
"""

import os
import pandas as pd
import numpy as np
import time
import logging
from sklearn.model_selection import train_test_split, cross_val_score, KFold
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.metrics import mean_squared_error, r2_score
import xgboost as xgb
import optuna
import warnings
warnings.filterwarnings('ignore')

# 导入GPU加速模块
from gpu_accelerated_training import get_gpu_trainer, create_gpu_optimized_xgboost_regressor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 初始化GPU训练器
gpu_trainer = get_gpu_trainer()

def load_speed_data():
    """加载速度预测数据"""
    filepath = './training_datasets_clean/speed_regression.csv'
    
    df = pd.read_csv(filepath)
    logger.info(f"原始数据形状: {df.shape}")
    
    # 分离特征和目标
    target_col = 'speed_kmh'
    feature_cols = [col for col in df.columns 
                  if col not in [target_col, 'experiment_id', 'sensor_id', 'timestamp']]
    
    X = df[feature_cols].select_dtypes(include=[np.number])
    y = df[target_col]
    
    # 数据清理
    X = X.fillna(X.median())
    
    # 移除异常值
    Q1 = y.quantile(0.25)
    Q3 = y.quantile(0.75)
    IQR = Q3 - Q1
    lower_bound = Q1 - 1.5 * IQR
    upper_bound = Q3 + 1.5 * IQR
    valid_mask = (y >= lower_bound) & (y <= upper_bound)
    X = X[valid_mask]
    y = y[valid_mask]
    
    logger.info(f"清理后数据形状: {X.shape}")
    logger.info(f"目标值范围: [{y.min():.2f}, {y.max():.2f}]")
    
    return X, y

def optimize_random_forest(X, y, n_trials=20):
    """优化Random Forest"""
    logger.info("优化Random Forest...")
    
    def objective(trial):
        params = {
            'n_estimators': trial.suggest_int('n_estimators', 200, 500),
            'max_depth': trial.suggest_int('max_depth', 10, 20),
            'min_samples_split': trial.suggest_int('min_samples_split', 2, 10),
            'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 4),
            'max_features': trial.suggest_categorical('max_features', [0.3, 0.5, 'sqrt']),
            'random_state': 42,
            'n_jobs': -1
        }
        
        model = RandomForestRegressor(**params)
        cv = KFold(n_splits=5, shuffle=True, random_state=42)
        scores = cross_val_score(model, X, y, cv=cv, scoring='r2')
        return scores.mean()
    
    study = optuna.create_study(direction='maximize')
    study.optimize(objective, n_trials=n_trials)
    
    logger.info(f"Random Forest最佳参数: {study.best_params}")
    logger.info(f"Random Forest最佳R²: {study.best_value:.4f}")
    
    return study.best_params, study.best_value

def optimize_gradient_boosting(X, y, n_trials=20):
    """优化Gradient Boosting"""
    logger.info("优化Gradient Boosting...")
    
    def objective(trial):
        params = {
            'n_estimators': trial.suggest_int('n_estimators', 200, 400),
            'learning_rate': trial.suggest_float('learning_rate', 0.05, 0.2),
            'max_depth': trial.suggest_int('max_depth', 4, 8),
            'min_samples_split': trial.suggest_int('min_samples_split', 2, 10),
            'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 8),
            'subsample': trial.suggest_float('subsample', 0.8, 1.0),
            'random_state': 42
        }
        
        model = GradientBoostingRegressor(**params)
        cv = KFold(n_splits=5, shuffle=True, random_state=42)
        scores = cross_val_score(model, X, y, cv=cv, scoring='r2')
        return scores.mean()
    
    study = optuna.create_study(direction='maximize')
    study.optimize(objective, n_trials=n_trials)
    
    logger.info(f"Gradient Boosting最佳参数: {study.best_params}")
    logger.info(f"Gradient Boosting最佳R²: {study.best_value:.4f}")
    
    return study.best_params, study.best_value

def optimize_xgboost(X, y, n_trials=20):
    """优化XGBoost (GPU加速版)"""
    logger.info("优化XGBoost (GPU加速)...")

    def objective(trial):
        base_params = {
            'n_estimators': trial.suggest_int('n_estimators', 200, 400),
            'learning_rate': trial.suggest_float('learning_rate', 0.05, 0.2),
            'max_depth': trial.suggest_int('max_depth', 4, 8),
            'min_child_weight': trial.suggest_int('min_child_weight', 1, 8),
            'subsample': trial.suggest_float('subsample', 0.8, 1.0),
            'colsample_bytree': trial.suggest_float('colsample_bytree', 0.8, 1.0),
            'reg_alpha': trial.suggest_float('reg_alpha', 0, 0.5),
            'reg_lambda': trial.suggest_float('reg_lambda', 0, 0.5),
            'random_state': 42,
            'eval_metric': 'rmse'
        }

        # 使用GPU优化参数
        params = gpu_trainer.get_xgboost_params(base_params)
        model = xgb.XGBRegressor(**params)

        cv = KFold(n_splits=5, shuffle=True, random_state=42)
        scores = cross_val_score(model, X, y, cv=cv, scoring='r2')
        return scores.mean()

    study = optuna.create_study(direction='maximize')
    study.optimize(objective, n_trials=n_trials)

    logger.info(f"XGBoost最佳参数: {study.best_params}")
    logger.info(f"XGBoost最佳R²: {study.best_value:.4f}")

    return study.best_params, study.best_value

def train_and_evaluate_models(X, y, best_params):
    """训练和评估最佳模型"""
    logger.info("训练和评估最佳模型...")
    
    # 数据分割
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42
    )
    
    results = {}
    
    # 1. 优化后的Random Forest
    if 'rf' in best_params:
        rf_params = best_params['rf'][0]
        rf_params['random_state'] = 42
        rf_params['n_jobs'] = -1
        
        rf_model = RandomForestRegressor(**rf_params)
        rf_model.fit(X_train, y_train)
        rf_pred = rf_model.predict(X_test)
        
        rf_r2 = r2_score(y_test, rf_pred)
        rf_rmse = np.sqrt(mean_squared_error(y_test, rf_pred))
        
        results['Optimized_RandomForest'] = {
            'r2': rf_r2,
            'rmse': rf_rmse,
            'cv_score': best_params['rf'][1]
        }
        logger.info(f"优化Random Forest - R²: {rf_r2:.4f}, RMSE: {rf_rmse:.4f}")
    
    # 2. 优化后的Gradient Boosting
    if 'gb' in best_params:
        gb_params = best_params['gb'][0]
        gb_params['random_state'] = 42
        
        gb_model = GradientBoostingRegressor(**gb_params)
        gb_model.fit(X_train, y_train)
        gb_pred = gb_model.predict(X_test)
        
        gb_r2 = r2_score(y_test, gb_pred)
        gb_rmse = np.sqrt(mean_squared_error(y_test, gb_pred))
        
        results['Optimized_GradientBoosting'] = {
            'r2': gb_r2,
            'rmse': gb_rmse,
            'cv_score': best_params['gb'][1]
        }
        logger.info(f"优化Gradient Boosting - R²: {gb_r2:.4f}, RMSE: {gb_rmse:.4f}")
    
    # 3. 优化后的XGBoost (GPU加速)
    if 'xgb' in best_params:
        base_xgb_params = best_params['xgb'][0]
        base_xgb_params['random_state'] = 42

        # 应用GPU优化
        xgb_params = gpu_trainer.get_xgboost_params(base_xgb_params)
        xgb_model = xgb.XGBRegressor(**xgb_params)

        # 使用GPU优化训练
        gpu_trainer.train_with_gpu_optimization(xgb_model, X_train, y_train)
        xgb_pred = xgb_model.predict(X_test)

        xgb_r2 = r2_score(y_test, xgb_pred)
        xgb_rmse = np.sqrt(mean_squared_error(y_test, xgb_pred))

        results['Optimized_XGBoost_GPU'] = {
            'r2': xgb_r2,
            'rmse': xgb_rmse,
            'cv_score': best_params['xgb'][1]
        }
        logger.info(f"优化XGBoost (GPU) - R²: {xgb_r2:.4f}, RMSE: {xgb_rmse:.4f}")
    
    # 4. 基线Random Forest (未优化)
    baseline_rf = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
    baseline_rf.fit(X_train, y_train)
    baseline_pred = baseline_rf.predict(X_test)
    
    baseline_r2 = r2_score(y_test, baseline_pred)
    baseline_rmse = np.sqrt(mean_squared_error(y_test, baseline_pred))
    
    results['Baseline_RandomForest'] = {
        'r2': baseline_r2,
        'rmse': baseline_rmse,
        'cv_score': None
    }
    logger.info(f"基线Random Forest - R²: {baseline_r2:.4f}, RMSE: {baseline_rmse:.4f}")
    
    return results

def main():
    """主函数"""
    print("🚀 速度预测模型快速优化 (GPU加速版)")
    print("=" * 60)

    # 显示GPU状态
    print(gpu_trainer.get_optimization_summary())
    print()

    # 加载数据
    X, y = load_speed_data()
    
    # 优化模型
    best_params = {}
    
    print("\n📊 开始模型优化...")
    start_time = time.time()
    
    # 优化Random Forest
    rf_params, rf_score = optimize_random_forest(X, y, n_trials=15)
    best_params['rf'] = (rf_params, rf_score)
    
    # 优化Gradient Boosting
    gb_params, gb_score = optimize_gradient_boosting(X, y, n_trials=15)
    best_params['gb'] = (gb_params, gb_score)
    
    # 优化XGBoost
    xgb_params, xgb_score = optimize_xgboost(X, y, n_trials=15)
    best_params['xgb'] = (xgb_params, xgb_score)
    
    optimization_time = time.time() - start_time
    print(f"\n⏱️  优化完成，耗时: {optimization_time:.2f}秒")
    
    # 训练和评估
    print("\n🎯 训练和评估最佳模型...")
    results = train_and_evaluate_models(X, y, best_params)
    
    # 显示结果
    print(f"\n" + "="*50)
    print("🎉 速度预测优化结果")
    print("="*50)
    
    # 找到最佳模型
    best_model = max(results.items(), key=lambda x: x[1]['r2'])
    
    print(f"\n📈 性能对比:")
    for model_name, metrics in results.items():
        r2 = metrics['r2']
        rmse = metrics['rmse']
        cv_score = metrics.get('cv_score', 'N/A')
        
        status = "🏆" if model_name == best_model[0] else "  "
        print(f"{status} {model_name}:")
        print(f"     R² = {r2:.4f}, RMSE = {rmse:.4f}")
        if cv_score != 'N/A':
            print(f"     交叉验证R² = {cv_score:.4f}")
    
    # 检查是否达到目标
    best_r2 = best_model[1]['r2']
    target_r2 = 0.75
    
    print(f"\n🎯 目标检查:")
    print(f"   目标R²: {target_r2}")
    print(f"   最佳R²: {best_r2:.4f}")
    
    if best_r2 >= target_r2:
        print(f"   ✅ 已达到目标！提升了 {((best_r2 - 0.6796) / 0.6796 * 100):.1f}%")
    else:
        improvement_needed = ((target_r2 - best_r2) / best_r2 * 100)
        print(f"   ⚠️  还需提升 {improvement_needed:.1f}% 才能达到目标")
    
    # 保存结果
    import json
    with open('speed_optimization_results.json', 'w', encoding='utf-8') as f:
        json.dump({
            'best_params': best_params,
            'results': results,
            'optimization_time': optimization_time
        }, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 结果已保存到: speed_optimization_results.json")
    
    return results

if __name__ == "__main__":
    main()
