#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复主函数中的legacy_only模式支持
添加命令行参数支持，让用户可以选择数据源模式

作者: AI Assistant
日期: 2024-12-22
"""

def fix_main_function():
    """修复主函数，添加数据源模式支持"""
    print("🛠️  修复主函数中的数据源模式支持")
    print("="*80)
    
    # 读取当前的main函数
    with open('unified_vibration_analysis.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找并替换main函数
    old_main = '''def main():
    """主函数"""
    system = UnifiedVibrationAnalysisSystem()
    success = system.run_complete_analysis()
    
    if success:
        print(f"\\n✅ 系统运行成功!")
    else:
        print(f"\\n❌ 系统运行失败")
        print(f"\\n🔧 故障排除建议:")
        print(f"   1. 检查数据目录是否存在")
        print(f"   2. 确认所有依赖包已安装")
        print(f"   3. 查看错误信息进行调试")
    
    return success'''
    
    new_main = '''def main():
    """主函数"""
    import argparse
    
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='振动信号分析系统')
    parser.add_argument('--data-dir', type=str, default='./data',
                       help='数据目录路径 (默认: ./data)')
    parser.add_argument('--data-source-mode', type=str, default='dual_format',
                       choices=['legacy_only', 'dual_format', 'new_only'],
                       help='数据源处理模式 (默认: dual_format)')
    parser.add_argument('--force-extraction', action='store_true',
                       help='强制重新提取特征')
    
    args = parser.parse_args()
    
    print(f"🚀 启动振动信号分析系统")
    print(f"   数据目录: {args.data_dir}")
    print(f"   数据源模式: {args.data_source_mode}")
    print(f"   强制重新提取: {args.force_extraction}")
    print("="*80)
    
    # 初始化系统
    system = UnifiedVibrationAnalysisSystem(
        data_dir=args.data_dir,
        data_source_mode=args.data_source_mode
    )
    
    # 设置强制提取标志
    if args.force_extraction:
        system.force_feature_extraction = True
    
    success = system.run_complete_analysis()
    
    if success:
        print(f"\\n✅ 系统运行成功!")
        print(f"   数据源模式: {args.data_source_mode}")
        print(f"   处理的数据目录: {args.data_dir}")
    else:
        print(f"\\n❌ 系统运行失败")
        print(f"\\n🔧 故障排除建议:")
        print(f"   1. 检查数据目录是否存在: {args.data_dir}")
        print(f"   2. 确认数据源模式是否正确: {args.data_source_mode}")
        print(f"   3. 尝试使用 --force-extraction 强制重新提取特征")
        print(f"   4. 确认所有依赖包已安装")
        print(f"   5. 查看错误信息进行调试")
        print(f"\\n💡 使用示例:")
        print(f"   python unified_vibration_analysis.py --data-source-mode legacy_only")
        print(f"   python unified_vibration_analysis.py --data-source-mode new_only")
        print(f"   python unified_vibration_analysis.py --data-dir ./my_data --data-source-mode dual_format")
    
    return success'''
    
    # 替换main函数
    if old_main in content:
        new_content = content.replace(old_main, new_main)
        
        # 保存修改后的文件
        with open('unified_vibration_analysis.py', 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✅ 主函数修复完成")
        print("   ✅ 添加了命令行参数支持")
        print("   ✅ 支持数据源模式选择")
        print("   ✅ 支持自定义数据目录")
        print("   ✅ 支持强制重新提取特征")
        
        return True
    else:
        print("❌ 未找到目标main函数，可能已经被修改")
        return False

def create_usage_examples():
    """创建使用示例脚本"""
    print("\n📋 创建使用示例脚本")
    print("-"*50)
    
    examples_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
振动信号分析系统使用示例
展示如何使用不同的数据源模式

作者: AI Assistant
日期: 2024-12-22
"""

import subprocess
import sys

def run_example(description, command):
    """运行示例命令"""
    print(f"\\n🧪 {description}")
    print(f"命令: {command}")
    print("-"*60)
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 运行成功")
        else:
            print("❌ 运行失败")
            print(f"错误信息: {result.stderr}")
    except Exception as e:
        print(f"❌ 运行异常: {str(e)}")

def main():
    """主函数"""
    print("🚀 振动信号分析系统使用示例")
    print("="*80)
    
    examples = [
        {
            "description": "仅处理传统格式数据（21列CSV）",
            "command": "python unified_vibration_analysis.py --data-source-mode legacy_only"
        },
        {
            "description": "仅处理新格式数据（22列CSV）",
            "command": "python unified_vibration_analysis.py --data-source-mode new_only"
        },
        {
            "description": "同时处理两种格式数据（默认模式）",
            "command": "python unified_vibration_analysis.py --data-source-mode dual_format"
        },
        {
            "description": "使用自定义数据目录",
            "command": "python unified_vibration_analysis.py --data-dir ./my_data --data-source-mode legacy_only"
        },
        {
            "description": "强制重新提取特征",
            "command": "python unified_vibration_analysis.py --data-source-mode legacy_only --force-extraction"
        }
    ]
    
    print("💡 可用的使用示例:")
    for i, example in enumerate(examples, 1):
        print(f"\\n{i}. {example['description']}")
        print(f"   命令: {example['command']}")
    
    print(f"\\n📋 命令行参数说明:")
    print(f"   --data-dir: 指定数据目录路径")
    print(f"   --data-source-mode: 选择数据源处理模式")
    print(f"     - legacy_only: 仅处理传统格式数据")
    print(f"     - new_only: 仅处理新格式数据")
    print(f"     - dual_format: 同时处理两种格式（默认）")
    print(f"   --force-extraction: 强制重新提取特征")
    
    print(f"\\n🎯 针对用户问题的解决方案:")
    print(f"   如果遇到 'legacy_only' 模式错误，请使用:")
    print(f"   python unified_vibration_analysis.py --data-source-mode legacy_only")

if __name__ == "__main__":
    main()'''
    
    with open('usage_examples.py', 'w', encoding='utf-8') as f:
        f.write(examples_content)
    
    print("✅ 使用示例脚本已创建: usage_examples.py")

def test_fixed_main_function():
    """测试修复后的主函数"""
    print("\n🧪 测试修复后的主函数")
    print("-"*50)
    
    try:
        # 测试帮助信息
        import subprocess
        result = subprocess.run([
            'python', 'unified_vibration_analysis.py', '--help'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ 命令行参数解析正常")
            print("   帮助信息:")
            for line in result.stdout.split('\\n')[:10]:
                if line.strip():
                    print(f"      {line}")
        else:
            print("❌ 命令行参数解析失败")
            print(f"   错误: {result.stderr}")
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 启动主函数修复程序")
    
    # 执行修复
    fix_success = fix_main_function()
    
    if fix_success:
        # 创建使用示例
        create_usage_examples()
        
        # 测试修复效果
        test_success = test_fixed_main_function()
        
        if test_success:
            print(f"\\n🎉 修复完成！")
            print(f"\\n💡 现在用户可以使用以下命令:")
            print(f"   python unified_vibration_analysis.py --data-source-mode legacy_only")
            print(f"   python unified_vibration_analysis.py --data-source-mode new_only")
            print(f"   python unified_vibration_analysis.py --data-source-mode dual_format")
            
            print(f"\\n🎯 解决用户问题:")
            print(f"   用户遇到的 'legacy_only' 模式错误现在应该已经解决")
            print(f"   系统现在支持通过命令行参数选择数据源模式")
            
            return True
        else:
            print(f"\\n⚠️  修复完成但测试未通过")
            return False
    else:
        print(f"\\n❌ 修复失败")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
