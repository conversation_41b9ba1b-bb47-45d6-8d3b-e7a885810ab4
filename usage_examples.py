#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
振动信号分析系统使用示例
展示如何使用不同的数据源模式

作者: AI Assistant
日期: 2024-12-22
"""

import subprocess
import sys

def run_example(description, command):
    """运行示例命令"""
    print(f"\n🧪 {description}")
    print(f"命令: {command}")
    print("-"*60)
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 运行成功")
        else:
            print("❌ 运行失败")
            print(f"错误信息: {result.stderr}")
    except Exception as e:
        print(f"❌ 运行异常: {str(e)}")

def main():
    """主函数"""
    print("🚀 振动信号分析系统使用示例")
    print("="*80)
    
    examples = [
        {
            "description": "仅处理传统格式数据（21列CSV）",
            "command": "python unified_vibration_analysis.py --data-source-mode legacy_only"
        },
        {
            "description": "仅处理新格式数据（22列CSV）",
            "command": "python unified_vibration_analysis.py --data-source-mode new_only"
        },
        {
            "description": "同时处理两种格式数据（默认模式）",
            "command": "python unified_vibration_analysis.py --data-source-mode dual_format"
        },
        {
            "description": "使用自定义数据目录",
            "command": "python unified_vibration_analysis.py --data-dir ./my_data --data-source-mode legacy_only"
        },
        {
            "description": "强制重新提取特征",
            "command": "python unified_vibration_analysis.py --data-source-mode legacy_only --force-extraction"
        }
    ]
    
    print("💡 可用的使用示例:")
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['description']}")
        print(f"   命令: {example['command']}")
    
    print(f"\n📋 命令行参数说明:")
    print(f"   --data-dir: 指定数据目录路径")
    print(f"   --data-source-mode: 选择数据源处理模式")
    print(f"     - legacy_only: 仅处理传统格式数据")
    print(f"     - new_only: 仅处理新格式数据")
    print(f"     - dual_format: 同时处理两种格式（默认）")
    print(f"   --force-extraction: 强制重新提取特征")
    
    print(f"\n🎯 针对用户问题的解决方案:")
    print(f"   如果遇到 'legacy_only' 模式错误，请使用:")
    print(f"   python unified_vibration_analysis.py --data-source-mode legacy_only")

if __name__ == "__main__":
    main()