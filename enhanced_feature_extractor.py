#!/usr/bin/env python3
"""
增强的特征提取模块
专为统一振动信号分析系统设计，确保能够从原始CSV文件提取完整特征
"""

import os
import pandas as pd
import numpy as np
from scipy import signal
from scipy.fft import fft, fftfreq
import warnings
warnings.filterwarnings('ignore')

class EnhancedFeatureExtractor:
    """增强的特征提取器"""
    
    def __init__(self, fs=1000):
        """
        初始化特征提取器
        
        Args:
            fs: 采样频率 (Hz)
        """
        self.fs = fs
        
    def extract_time_domain_features(self, signal: np.ndarray) -> dict:
        """提取时域特征"""
        try:
            if len(signal) == 0:
                return {}
            
            # 基本统计特征
            features = {
                'mean': np.mean(signal),
                'std': np.std(signal),
                'var': np.var(signal),
                'rms': np.sqrt(np.mean(signal**2)),
                'peak': np.max(np.abs(signal)),
                'peak_to_peak': np.max(signal) - np.min(signal),
                'energy': np.sum(signal**2),
                'power': np.mean(signal**2)
            }
            
            # 高级统计特征
            if np.std(signal) > 0:
                features['skewness'] = self._calculate_skewness(signal)
                features['kurtosis'] = self._calculate_kurtosis(signal)
                features['crest_factor'] = features['peak'] / features['rms'] if features['rms'] > 0 else 0
                features['clearance_factor'] = features['peak'] / np.mean(np.sqrt(np.abs(signal)))**2 if np.mean(np.sqrt(np.abs(signal))) > 0 else 0
                features['impulse_factor'] = features['peak'] / np.mean(np.abs(signal)) if np.mean(np.abs(signal)) > 0 else 0
                features['shape_factor'] = features['rms'] / np.mean(np.abs(signal)) if np.mean(np.abs(signal)) > 0 else 0
            
            # 过零率
            features['zero_crossing_rate'] = self._zero_crossing_rate(signal)
            
            return features
            
        except Exception as e:
            print(f"      时域特征提取失败: {str(e)}")
            return {}
    
    def extract_frequency_domain_features(self, signal: np.ndarray) -> dict:
        """提取频域特征"""
        try:
            if len(signal) < 4:
                return {}
            
            # FFT变换
            fft_signal = fft(signal)
            freqs = fftfreq(len(signal), 1/self.fs)
            magnitude = np.abs(fft_signal)
            
            # 只取正频率部分
            positive_freqs = freqs[:len(freqs)//2]
            positive_magnitude = magnitude[:len(magnitude)//2]
            
            if len(positive_magnitude) == 0 or np.sum(positive_magnitude) == 0:
                return {}
            
            # 功率谱
            power_spectrum = positive_magnitude**2
            total_power = np.sum(power_spectrum)
            
            features = {}
            
            # 主频率
            features['dominant_frequency'] = positive_freqs[np.argmax(positive_magnitude)]
            
            # 频谱重心
            if total_power > 0:
                features['spectral_centroid'] = np.sum(positive_freqs * power_spectrum) / total_power
                features['spectral_spread'] = np.sqrt(np.sum(((positive_freqs - features['spectral_centroid'])**2) * power_spectrum) / total_power)
            else:
                features['spectral_centroid'] = 0
                features['spectral_spread'] = 0
            
            # 频谱滚降点
            features['spectral_rolloff'] = self._spectral_rolloff(positive_freqs, positive_magnitude)
            
            # 频谱通量
            features['spectral_flux'] = np.sum(np.diff(positive_magnitude)**2)
            
            # 频带能量分布
            features['total_power'] = total_power
            features['low_freq_power'] = np.sum(power_spectrum[positive_freqs <= 50])
            features['mid_freq_power'] = np.sum(power_spectrum[(positive_freqs > 50) & (positive_freqs <= 150)])
            features['high_freq_power'] = np.sum(power_spectrum[positive_freqs > 150])
            
            # 频带能量比例
            if total_power > 0:
                features['low_freq_ratio'] = features['low_freq_power'] / total_power
                features['mid_freq_ratio'] = features['mid_freq_power'] / total_power
                features['high_freq_ratio'] = features['high_freq_power'] / total_power
            
            return features
            
        except Exception as e:
            print(f"      频域特征提取失败: {str(e)}")
            return {}
    
    def extract_all_features(self, signal: np.ndarray, sensor_name: str = "") -> dict:
        """提取所有特征"""
        try:
            # 数据预处理
            signal = np.array(signal)
            signal = signal[~np.isnan(signal)]  # 移除NaN
            signal = signal[~np.isinf(signal)]  # 移除无穷大
            
            if len(signal) < 10:
                return {}
            
            all_features = {}
            
            # 时域特征
            time_features = self.extract_time_domain_features(signal)
            for key, value in time_features.items():
                all_features[f'{sensor_name}_{key}'] = value
            
            # 频域特征
            freq_features = self.extract_frequency_domain_features(signal)
            for key, value in freq_features.items():
                all_features[f'{sensor_name}_{key}'] = value
            
            return all_features
            
        except Exception as e:
            print(f"      特征提取失败 ({sensor_name}): {str(e)}")
            return {}
    
    def _calculate_skewness(self, data):
        """计算偏度"""
        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0
        return np.mean(((data - mean) / std) ** 3)
    
    def _calculate_kurtosis(self, data):
        """计算峰度"""
        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0
        return np.mean(((data - mean) / std) ** 4) - 3
    
    def _zero_crossing_rate(self, signal):
        """计算过零率"""
        if len(signal) <= 1:
            return 0
        return np.sum(np.diff(np.sign(signal)) != 0) / len(signal)
    
    def _spectral_rolloff(self, freqs, magnitude, rolloff_percent=0.85):
        """计算频谱滚降点"""
        total_energy = np.sum(magnitude)
        if total_energy == 0:
            return 0
        
        cumulative_energy = np.cumsum(magnitude)
        rolloff_index = np.where(cumulative_energy >= rolloff_percent * total_energy)[0]
        
        if len(rolloff_index) > 0:
            return freqs[rolloff_index[0]]
        else:
            return freqs[-1] if len(freqs) > 0 else 0

class VibrationDataProcessor:
    """振动数据处理器"""
    
    def __init__(self, fs=1000):
        """初始化处理器"""
        self.fs = fs
        self.feature_extractor = EnhancedFeatureExtractor(fs)
    
    def safe_read_csv(self, file_path: str) -> pd.DataFrame:
        """安全读取CSV文件"""
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin1', 'cp1252']
        
        for encoding in encodings:
            try:
                return pd.read_csv(file_path, encoding=encoding)
            except UnicodeDecodeError:
                continue
            except Exception:
                break
        return None
    
    def extract_path_info(self, file_path: str) -> dict:
        """从文件路径提取标签信息"""
        path_parts = file_path.replace('\\', '/').split('/')
        
        info = {
            'speed_kmh': None,
            'load_tons': None,
            'axle_type': None,
            'axle_count': None
        }
        
        for part in path_parts:
            # 提取速度信息
            if 'km_h' in part or 'km/h' in part:
                try:
                    speed_str = part.replace('km_h', '').replace('km/h', '').replace('_repeat', '').replace('_', '')
                    info['speed_kmh'] = float(speed_str)
                except:
                    pass
            
            # 提取载重信息
            if '吨' in part:
                try:
                    load_str = part.replace('吨', '').replace('_', '').replace('-', '')
                    info['load_tons'] = float(load_str)
                except:
                    pass
            
            # 提取轴型信息
            if '双轴' in part:
                info['axle_type'] = 2
                info['axle_count'] = 2
            elif '三轴' in part:
                info['axle_type'] = 3
                info['axle_count'] = 3
        
        return info
    
    def process_single_file(self, file_path: str) -> dict:
        """处理单个CSV文件"""
        try:
            # 读取文件
            df = self.safe_read_csv(file_path)
            if df is None:
                return None
            
            # 提取路径信息
            path_info = self.extract_path_info(file_path)
            
            # 获取传感器列
            sensor_columns = [col for col in df.columns if 
                            ('sensor' in col.lower() or 'acce' in col.lower()) and 
                            col.lower() not in ['time', 'timestamp']]
            
            if not sensor_columns:
                return None
            
            # 初始化特征字典
            features = {
                'file_path': file_path,
                'file_name': os.path.basename(file_path),
                'speed_kmh': path_info.get('speed_kmh'),
                'load_tons': path_info.get('load_tons'),
                'axle_type': path_info.get('axle_type'),
                'axle_count': path_info.get('axle_count'),
                'sensor_count': len(sensor_columns),
                'data_length': len(df)
            }
            
            # 为每个传感器提取特征
            for col in sensor_columns[:20]:  # 限制传感器数量
                try:
                    signal = df[col].dropna().values
                    if len(signal) > 10:
                        sensor_features = self.feature_extractor.extract_all_features(signal, col)
                        features.update(sensor_features)
                except Exception as e:
                    continue
            
            return features
            
        except Exception as e:
            print(f"   处理文件失败 {os.path.basename(file_path)}: {str(e)}")
            return None
    
    def process_directory(self, data_dir: str) -> pd.DataFrame:
        """处理整个数据目录"""
        print(f"🔄 处理数据目录: {data_dir}")
        
        all_features = []
        file_count = 0
        
        # 遍历所有CSV文件
        for root, dirs, files in os.walk(data_dir):
            for file in files:
                if file.endswith('.csv') and ('acce_' in file or 'sensor' in file or 'legacy_' in file):
                    file_path = os.path.join(root, file)
                    
                    features = self.process_single_file(file_path)
                    if features:
                        all_features.append(features)
                        file_count += 1
                        
                        if file_count % 20 == 0:
                            print(f"   已处理 {file_count} 个文件...")
        
        if all_features:
            features_df = pd.DataFrame(all_features)
            print(f"✅ 数据处理完成")
            print(f"   处理文件数: {file_count}")
            print(f"   特征数据形状: {features_df.shape}")
            
            return features_df
        else:
            print("❌ 没有成功处理任何文件")
            return None

def main():
    """测试函数"""
    processor = VibrationDataProcessor()
    
    # 测试数据目录
    test_dirs = ['./data', '../data', './your_data']
    
    for data_dir in test_dirs:
        if os.path.exists(data_dir):
            print(f"🧪 测试处理目录: {data_dir}")
            features_df = processor.process_directory(data_dir)
            
            if features_df is not None:
                # 保存特征文件
                features_df.to_csv('enhanced_features.csv', index=False)
                print(f"✅ 特征已保存到: enhanced_features.csv")
                break
            else:
                print(f"❌ 目录处理失败: {data_dir}")

if __name__ == "__main__":
    main()
