#!/usr/bin/env python3
"""
GPU加速的综合优化训练脚本
集成所有GPU优化功能
"""

import os
import sys
import pandas as pd
import numpy as np
import time
import logging
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score, accuracy_score
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from gpu_accelerated_training import get_gpu_trainer
from model_optimization import BayesianOptimizer
from model_replacer import ModelReplacer
from performance_comparison import PerformanceComparator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('gpu_optimization.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class GPUOptimizedMLTrainer:
    """GPU优化的机器学习训练器"""
    
    def __init__(self, n_trials: int = 30, cv_folds: int = 5, random_state: int = 42):
        """
        初始化GPU优化训练器
        
        Args:
            n_trials: 贝叶斯优化试验次数
            cv_folds: 交叉验证折数
            random_state: 随机种子
        """
        self.n_trials = n_trials
        self.cv_folds = cv_folds
        self.random_state = random_state
        self.gpu_trainer = get_gpu_trainer()
        self.results = {}
        
        logger.info(f"初始化GPU优化训练器: n_trials={n_trials}, cv_folds={cv_folds}")
        logger.info(self.gpu_trainer.get_optimization_summary())
    
    def load_and_prepare_data(self, filepath: str, task_type: str):
        """加载和准备数据"""
        logger.info(f"加载数据集: {filepath}")
        
        try:
            df = pd.read_csv(filepath)
            logger.info(f"原始数据形状: {df.shape}")
            
            # 确定目标列
            if task_type == 'regression':
                if 'axle_load_tons' in df.columns:
                    target_col = 'axle_load_tons'
                elif 'speed_kmh' in df.columns:
                    target_col = 'speed_kmh'
                else:
                    raise ValueError("未找到回归目标列")
            else:  # classification
                target_col = 'axle_type'
            
            # 分离特征和目标
            feature_cols = [col for col in df.columns 
                          if col not in [target_col, 'experiment_id', 'sensor_id', 'timestamp']]
            
            X = df[feature_cols].select_dtypes(include=[np.number])
            y = df[target_col]
            
            # 数据清理
            X = X.fillna(X.median())
            
            # 移除异常值
            if task_type == 'regression':
                Q1 = y.quantile(0.25)
                Q3 = y.quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                valid_mask = (y >= lower_bound) & (y <= upper_bound)
                X = X[valid_mask]
                y = y[valid_mask]
            
            logger.info(f"清理后数据形状: {X.shape}")
            logger.info(f"特征数量: {X.shape[1]}")
            logger.info(f"目标列: {target_col}")
            
            if task_type == 'classification':
                logger.info(f"类别分布: {y.value_counts().to_dict()}")
            else:
                logger.info(f"目标值范围: [{y.min():.2f}, {y.max():.2f}]")
            
            return X, y, target_col
            
        except Exception as e:
            logger.error(f"数据加载失败: {str(e)}")
            return None, None, None
    
    def optimize_with_gpu_acceleration(self, X: np.ndarray, y: np.ndarray, 
                                     task_type: str, task_name: str):
        """使用GPU加速进行模型优化"""
        logger.info(f"开始{task_name}的GPU加速优化...")
        
        # 创建GPU优化的贝叶斯优化器
        optimizer = BayesianOptimizer(
            task_type=task_type,
            cv_folds=self.cv_folds,
            n_trials=self.n_trials,
            random_state=self.random_state
        )
        
        # 修改优化器以使用GPU加速的XGBoost
        original_optimize_xgboost = optimizer.optimize_xgboost
        
        def gpu_optimize_xgboost(X, y):
            logger.info("开始GPU加速XGBoost优化...")
            
            def objective(trial):
                base_params = {
                    'n_estimators': trial.suggest_int('n_estimators', 200, 500),
                    'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.2),
                    'max_depth': trial.suggest_int('max_depth', 4, 10),
                    'min_child_weight': trial.suggest_int('min_child_weight', 1, 8),
                    'subsample': trial.suggest_float('subsample', 0.8, 1.0),
                    'colsample_bytree': trial.suggest_float('colsample_bytree', 0.8, 1.0),
                    'reg_alpha': trial.suggest_float('reg_alpha', 0, 0.5),
                    'reg_lambda': trial.suggest_float('reg_lambda', 0, 0.5),
                    'random_state': self.random_state
                }
                
                # 应用GPU优化
                params = self.gpu_trainer.get_xgboost_params(base_params)
                
                if task_type == 'regression':
                    import xgboost as xgb
                    model = xgb.XGBRegressor(**params)
                else:
                    import xgboost as xgb
                    model = xgb.XGBClassifier(**params)
                
                from sklearn.model_selection import cross_val_score, StratifiedKFold, KFold
                if task_type == 'regression':
                    cv = KFold(n_splits=self.cv_folds, shuffle=True, random_state=self.random_state)
                    scoring = 'r2'
                else:
                    cv = StratifiedKFold(n_splits=self.cv_folds, shuffle=True, random_state=self.random_state)
                    scoring = 'accuracy'
                
                scores = cross_val_score(model, X, y, cv=cv, scoring=scoring)
                return scores.mean()
            
            import optuna
            study = optuna.create_study(direction='maximize', 
                                      sampler=optuna.samplers.TPESampler(seed=self.random_state))
            study.optimize(objective, n_trials=self.n_trials)
            
            optimizer.best_params['XGBoost'] = study.best_params
            optimizer.best_scores['XGBoost'] = study.best_value
            optimizer.optimization_history['XGBoost'] = study.trials
            
            logger.info(f"GPU XGBoost最佳参数: {study.best_params}")
            logger.info(f"GPU XGBoost最佳分数: {study.best_value:.4f}")
            
            return study.best_params
        
        # 替换XGBoost优化方法
        optimizer.optimize_xgboost = gpu_optimize_xgboost
        
        # 执行优化
        start_time = time.time()
        optimization_results = optimizer.optimize_all_models(X, y)
        end_time = time.time()
        
        logger.info(f"{task_name}GPU优化完成，耗时: {end_time - start_time:.2f}秒")
        
        # 保存优化结果
        results_file = f'gpu_optimization_results_{task_name.lower().replace(" ", "_")}.json'
        optimizer.save_optimization_results(results_file)
        
        return optimization_results
    
    def train_best_gpu_models(self, X: np.ndarray, y: np.ndarray, task_type: str,
                            optimization_results: dict):
        """训练最佳GPU优化模型"""
        logger.info("训练最佳GPU优化模型...")
        
        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=self.random_state,
            stratify=y if task_type == 'classification' else None
        )
        
        results = {}
        best_params = optimization_results.get('best_params', {})
        
        # 训练GPU优化的XGBoost
        if 'XGBoost' in best_params:
            import xgboost as xgb
            
            base_params = best_params['XGBoost']
            base_params['random_state'] = self.random_state
            
            # 应用GPU优化
            gpu_params = self.gpu_trainer.get_xgboost_params(base_params)
            
            if task_type == 'regression':
                model = xgb.XGBRegressor(**gpu_params)
            else:
                model = xgb.XGBClassifier(**gpu_params)
            
            # 使用GPU优化训练
            self.gpu_trainer.train_with_gpu_optimization(model, X_train, y_train)
            y_pred = model.predict(X_test)
            
            if task_type == 'regression':
                r2 = r2_score(y_test, y_pred)
                rmse = np.sqrt(mean_squared_error(y_test, y_pred))
                results['GPU_Optimized_XGBoost'] = {
                    'model': model,
                    'r2': r2,
                    'rmse': rmse,
                    'predictions': y_pred
                }
                logger.info(f"GPU优化XGBoost - R²: {r2:.4f}, RMSE: {rmse:.4f}")
            else:
                accuracy = accuracy_score(y_test, y_pred)
                results['GPU_Optimized_XGBoost'] = {
                    'model': model,
                    'accuracy': accuracy,
                    'predictions': y_pred
                }
                logger.info(f"GPU优化XGBoost - 准确率: {accuracy:.4f}")
        
        return results, (X_test, y_test)
    
    def train_single_task_gpu(self, filepath: str, task_type: str, task_name: str):
        """使用GPU加速训练单个任务"""
        logger.info(f"开始GPU加速训练任务: {task_name}")
        
        # 1. 加载数据
        X, y, target_col = self.load_and_prepare_data(filepath, task_type)
        if X is None:
            return None
        
        # 2. GPU加速优化
        optimization_results = self.optimize_with_gpu_acceleration(X, y, task_type, task_name)
        
        # 3. 训练最佳GPU模型
        best_results, test_data = self.train_best_gpu_models(
            X, y, task_type, optimization_results
        )
        
        return {
            'task_name': task_name,
            'task_type': task_type,
            'target_col': target_col,
            'optimization_results': optimization_results,
            'best_results': best_results,
            'test_data': test_data,
            'gpu_status': self.gpu_trainer.gpu_status
        }

def main():
    """主函数"""
    print("🚀 GPU加速机器学习优化系统")
    print("=" * 60)
    
    # 初始化GPU训练器
    trainer = GPUOptimizedMLTrainer(n_trials=25, cv_folds=5)
    
    # 检查数据集
    datasets = [
        ('./training_datasets/speed_regression.csv', 'regression', '速度预测'),
        ('./training_datasets/load_regression.csv', 'regression', '轴重预测'),
        ('./ml/combined_features_clean.csv', 'classification', '轴型分类')
    ]
    
    available_datasets = []
    for filepath, task_type, task_name in datasets:
        if os.path.exists(filepath):
            available_datasets.append((filepath, task_type, task_name))
            print(f"✅ {task_name}: {filepath}")
        else:
            print(f"❌ {task_name}: {filepath} (文件不存在)")
    
    if not available_datasets:
        print("\n❌ 没有可用的数据集文件")
        return False
    
    # 训练所有任务
    all_results = {}
    
    for filepath, task_type, task_name in available_datasets:
        try:
            result = trainer.train_single_task_gpu(filepath, task_type, task_name)
            if result:
                all_results[task_name] = result
                logger.info(f"✅ {task_name} GPU训练完成")
            else:
                logger.error(f"❌ {task_name} GPU训练失败")
        except Exception as e:
            logger.error(f"❌ {task_name} GPU训练异常: {str(e)}")
            continue
    
    # 生成总结
    if all_results:
        print(f"\n" + "="*60)
        print("🎉 GPU优化训练总结")
        print("="*60)
        
        for task_name, result in all_results.items():
            print(f"\n{task_name}:")
            
            # 显示GPU状态
            gpu_status = result.get('gpu_status', {})
            if gpu_status.get('xgboost_gpu', False):
                print(f"  🚀 XGBoost GPU加速: 已启用")
            else:
                print(f"  💻 XGBoost GPU加速: 未启用")
            
            # 显示最终结果
            best_results = result.get('best_results', {})
            for model_name, model_result in best_results.items():
                if result['task_type'] == 'regression':
                    print(f"  {model_name}: R² = {model_result['r2']:.4f}, RMSE = {model_result['rmse']:.4f}")
                else:
                    print(f"  {model_name}: 准确率 = {model_result['accuracy']:.4f}")
        
        print(f"\n🎉 所有GPU优化任务完成！")
        print(f"📊 查看生成的日志和结果文件了解详细信息")
        return True
    else:
        print(f"\n❌ 所有任务都失败了")
        return False

if __name__ == "__main__":
    main()
