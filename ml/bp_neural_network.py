#!/usr/bin/env python3
"""
BP神经网络（反向传播神经网络）实现
支持GPU加速训练和贝叶斯优化
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import KFold, StratifiedKFold
from sklearn.metrics import r2_score, accuracy_score, mean_squared_error
import optuna
import logging
import time
from typing import Dict, Any, Tuple, Optional, List
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BPNeuralNetwork(nn.Module):
    """BP神经网络模型"""
    
    def __init__(self, input_size: int, hidden_sizes: List[int], output_size: int, 
                 activation: str = 'relu', dropout_rate: float = 0.2):
        """
        初始化BP神经网络
        
        Args:
            input_size: 输入特征维度
            hidden_sizes: 隐藏层神经元数量列表
            output_size: 输出维度
            activation: 激活函数 ('relu', 'sigmoid', 'tanh')
            dropout_rate: Dropout比率
        """
        super(BPNeuralNetwork, self).__init__()
        
        self.input_size = input_size
        self.hidden_sizes = hidden_sizes
        self.output_size = output_size
        self.activation = activation
        self.dropout_rate = dropout_rate
        
        # 构建网络层
        layers = []
        prev_size = input_size
        
        # 隐藏层
        for hidden_size in hidden_sizes:
            layers.append(nn.Linear(prev_size, hidden_size))
            layers.append(self._get_activation_layer(activation))
            layers.append(nn.Dropout(dropout_rate))
            prev_size = hidden_size
        
        # 输出层
        layers.append(nn.Linear(prev_size, output_size))
        
        self.network = nn.Sequential(*layers)
        
        # 初始化权重
        self._initialize_weights()
    
    def _get_activation_layer(self, activation: str):
        """获取激活函数层"""
        if activation == 'relu':
            return nn.ReLU()
        elif activation == 'sigmoid':
            return nn.Sigmoid()
        elif activation == 'tanh':
            return nn.Tanh()
        else:
            raise ValueError(f"不支持的激活函数: {activation}")
    
    def _initialize_weights(self):
        """初始化网络权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                nn.init.constant_(module.bias, 0)
    
    def forward(self, x):
        """前向传播"""
        return self.network(x)

class BPNeuralNetworkTrainer:
    """BP神经网络训练器"""
    
    def __init__(self, task_type: str = 'regression', device: str = None):
        """
        初始化训练器
        
        Args:
            task_type: 任务类型 ('regression' 或 'classification')
            device: 计算设备 (None表示自动选择)
        """
        self.task_type = task_type
        self.device = device if device else ('cuda' if torch.cuda.is_available() else 'cpu')
        self.scaler = StandardScaler()
        
        logger.info(f"BP神经网络训练器初始化完成")
        logger.info(f"任务类型: {task_type}")
        logger.info(f"计算设备: {self.device}")
        
        if self.device == 'cuda':
            logger.info(f"GPU设备: {torch.cuda.get_device_name(0)}")
            logger.info(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    
    def prepare_data(self, X: np.ndarray, y: np.ndarray, batch_size: int = 32) -> Tuple[DataLoader, torch.Tensor, torch.Tensor]:
        """准备训练数据"""
        # 标准化特征
        X_scaled = self.scaler.fit_transform(X)
        
        # 转换为PyTorch张量
        X_tensor = torch.FloatTensor(X_scaled).to(self.device)
        
        if self.task_type == 'regression':
            y_tensor = torch.FloatTensor(y.reshape(-1, 1)).to(self.device)
        else:
            # 分类任务，确保标签从0开始
            unique_labels = np.unique(y)
            label_map = {label: idx for idx, label in enumerate(unique_labels)}
            y_mapped = np.array([label_map[label] for label in y])
            y_tensor = torch.LongTensor(y_mapped).to(self.device)
        
        # 创建数据集和数据加载器
        dataset = TensorDataset(X_tensor, y_tensor)
        dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)
        
        return dataloader, X_tensor, y_tensor
    
    def create_model(self, input_size: int, hidden_sizes: List[int], 
                    activation: str = 'relu', dropout_rate: float = 0.2) -> BPNeuralNetwork:
        """创建BP神经网络模型"""
        if self.task_type == 'regression':
            output_size = 1
        else:
            # 分类任务，输出维度为类别数
            output_size = len(np.unique(self.current_y)) if hasattr(self, 'current_y') else 2
        
        model = BPNeuralNetwork(
            input_size=input_size,
            hidden_sizes=hidden_sizes,
            output_size=output_size,
            activation=activation,
            dropout_rate=dropout_rate
        ).to(self.device)
        
        return model
    
    def train_model(self, model: BPNeuralNetwork, dataloader: DataLoader, 
                   epochs: int = 100, learning_rate: float = 0.001, 
                   weight_decay: float = 1e-4, patience: int = 10) -> Dict[str, List[float]]:
        """训练模型"""
        # 选择损失函数
        if self.task_type == 'regression':
            criterion = nn.MSELoss()
        else:
            criterion = nn.CrossEntropyLoss()
        
        # 优化器
        optimizer = optim.Adam(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
        
        # 学习率调度器
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=5, factor=0.5)
        
        # 训练历史
        history = {'loss': [], 'lr': []}
        best_loss = float('inf')
        patience_counter = 0
        
        model.train()
        for epoch in range(epochs):
            epoch_loss = 0.0
            num_batches = 0
            
            for batch_X, batch_y in dataloader:
                optimizer.zero_grad()
                
                # 前向传播
                outputs = model(batch_X)
                
                if self.task_type == 'regression':
                    loss = criterion(outputs, batch_y)
                else:
                    loss = criterion(outputs, batch_y)
                
                # 反向传播
                loss.backward()
                optimizer.step()
                
                epoch_loss += loss.item()
                num_batches += 1
            
            avg_loss = epoch_loss / num_batches
            history['loss'].append(avg_loss)
            history['lr'].append(optimizer.param_groups[0]['lr'])
            
            # 学习率调度
            scheduler.step(avg_loss)
            
            # 早停检查
            if avg_loss < best_loss:
                best_loss = avg_loss
                patience_counter = 0
            else:
                patience_counter += 1
                if patience_counter >= patience:
                    logger.info(f"早停触发，在第 {epoch+1} 轮停止训练")
                    break
            
            if (epoch + 1) % 20 == 0:
                logger.info(f"Epoch {epoch+1}/{epochs}, Loss: {avg_loss:.6f}, LR: {optimizer.param_groups[0]['lr']:.6f}")
        
        return history
    
    def evaluate_model(self, model: BPNeuralNetwork, X: torch.Tensor, y: torch.Tensor) -> float:
        """评估模型性能"""
        model.eval()
        with torch.no_grad():
            outputs = model(X)
            
            if self.task_type == 'regression':
                y_pred = outputs.cpu().numpy().flatten()
                y_true = y.cpu().numpy().flatten()
                score = r2_score(y_true, y_pred)
            else:
                y_pred = torch.argmax(outputs, dim=1).cpu().numpy()
                y_true = y.cpu().numpy()
                score = accuracy_score(y_true, y_pred)
        
        return score
    
    def cross_validate(self, X: np.ndarray, y: np.ndarray, hidden_sizes: List[int],
                      activation: str = 'relu', dropout_rate: float = 0.2,
                      epochs: int = 100, learning_rate: float = 0.001,
                      batch_size: int = 32, cv_folds: int = 5) -> float:
        """交叉验证评估"""
        self.current_y = y  # 保存y用于确定输出维度
        
        if self.task_type == 'regression':
            kf = KFold(n_splits=cv_folds, shuffle=True, random_state=42)
        else:
            kf = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)
        
        scores = []
        
        for fold, (train_idx, val_idx) in enumerate(kf.split(X, y)):
            X_train, X_val = X[train_idx], X[val_idx]
            y_train, y_val = y[train_idx], y[val_idx]
            
            # 准备训练数据
            train_loader, _, _ = self.prepare_data(X_train, y_train, batch_size)
            
            # 准备验证数据
            val_scaler = StandardScaler()
            X_val_scaled = val_scaler.fit_transform(X_val)
            X_val_tensor = torch.FloatTensor(X_val_scaled).to(self.device)
            
            if self.task_type == 'regression':
                y_val_tensor = torch.FloatTensor(y_val.reshape(-1, 1)).to(self.device)
            else:
                unique_labels = np.unique(y)
                label_map = {label: idx for idx, label in enumerate(unique_labels)}
                y_val_mapped = np.array([label_map[label] for label in y_val])
                y_val_tensor = torch.LongTensor(y_val_mapped).to(self.device)
            
            # 创建和训练模型
            model = self.create_model(X.shape[1], hidden_sizes, activation, dropout_rate)
            self.train_model(model, train_loader, epochs, learning_rate)
            
            # 评估
            score = self.evaluate_model(model, X_val_tensor, y_val_tensor)
            scores.append(score)
            
            logger.info(f"Fold {fold+1}/{cv_folds}: {score:.4f}")
        
        mean_score = np.mean(scores)
        logger.info(f"交叉验证平均分数: {mean_score:.4f} ± {np.std(scores):.4f}")
        
        return mean_score

class BPNeuralNetworkOptimizer:
    """BP神经网络贝叶斯优化器"""
    
    def __init__(self, task_type: str = 'regression', n_trials: int = 50, cv_folds: int = 5):
        """
        初始化优化器
        
        Args:
            task_type: 任务类型
            n_trials: 优化试验次数
            cv_folds: 交叉验证折数
        """
        self.task_type = task_type
        self.n_trials = n_trials
        self.cv_folds = cv_folds
        self.trainer = BPNeuralNetworkTrainer(task_type)
        self.best_params = None
        self.best_score = None
        
    def optimize(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """执行贝叶斯优化"""
        logger.info(f"开始BP神经网络贝叶斯优化...")
        logger.info(f"数据形状: {X.shape}, 任务类型: {self.task_type}")
        
        def objective(trial):
            # 网络结构参数
            n_layers = trial.suggest_int('n_layers', 2, 4)
            hidden_sizes = []
            for i in range(n_layers):
                size = trial.suggest_int(f'hidden_size_{i}', 64, 512)
                hidden_sizes.append(size)
            
            # 训练参数
            activation = trial.suggest_categorical('activation', ['relu', 'sigmoid', 'tanh'])
            dropout_rate = trial.suggest_float('dropout_rate', 0.1, 0.5)
            learning_rate = trial.suggest_float('learning_rate', 1e-4, 1e-2, log=True)
            batch_size = trial.suggest_categorical('batch_size', [16, 32, 64, 128])
            epochs = trial.suggest_int('epochs', 50, 200)
            
            try:
                score = self.trainer.cross_validate(
                    X, y, hidden_sizes, activation, dropout_rate,
                    epochs, learning_rate, batch_size, self.cv_folds
                )
                return score
            except Exception as e:
                logger.warning(f"试验失败: {str(e)}")
                return 0.0
        
        # 创建研究对象
        study = optuna.create_study(
            direction='maximize',
            sampler=optuna.samplers.TPESampler(seed=42)
        )
        
        # 执行优化
        study.optimize(objective, n_trials=self.n_trials)
        
        self.best_params = study.best_params
        self.best_score = study.best_value
        
        logger.info(f"BP神经网络最佳参数: {self.best_params}")
        logger.info(f"BP神经网络最佳分数: {self.best_score:.4f}")
        
        return {
            'best_params': self.best_params,
            'best_score': self.best_score,
            'study': study
        }

def create_optimized_bp_model(X: np.ndarray, y: np.ndarray, task_type: str = 'regression',
                             n_trials: int = 50) -> Tuple[BPNeuralNetwork, Dict[str, Any]]:
    """创建优化的BP神经网络模型"""
    optimizer = BPNeuralNetworkOptimizer(task_type, n_trials)
    optimization_results = optimizer.optimize(X, y)
    
    # 使用最佳参数创建模型
    trainer = BPNeuralNetworkTrainer(task_type)
    trainer.current_y = y
    
    best_params = optimization_results['best_params']
    
    # 提取隐藏层大小
    n_layers = best_params['n_layers']
    hidden_sizes = [best_params[f'hidden_size_{i}'] for i in range(n_layers)]
    
    model = trainer.create_model(
        input_size=X.shape[1],
        hidden_sizes=hidden_sizes,
        activation=best_params['activation'],
        dropout_rate=best_params['dropout_rate']
    )
    
    return model, optimization_results

if __name__ == "__main__":
    # 测试BP神经网络
    from sklearn.datasets import make_regression, make_classification
    
    print("🧪 测试BP神经网络...")
    
    # 测试回归
    print("\n📊 回归任务测试:")
    X_reg, y_reg = make_regression(n_samples=1000, n_features=20, noise=0.1, random_state=42)
    
    trainer_reg = BPNeuralNetworkTrainer('regression')
    score_reg = trainer_reg.cross_validate(X_reg, y_reg, [128, 64], epochs=50)
    print(f"回归任务交叉验证分数: {score_reg:.4f}")
    
    # 测试分类
    print("\n📊 分类任务测试:")
    X_cls, y_cls = make_classification(n_samples=1000, n_features=20, n_classes=3, n_informative=10, random_state=42)
    
    trainer_cls = BPNeuralNetworkTrainer('classification')
    score_cls = trainer_cls.cross_validate(X_cls, y_cls, [128, 64], epochs=50)
    print(f"分类任务交叉验证分数: {score_cls:.4f}")
    
    print("✅ BP神经网络测试完成")
