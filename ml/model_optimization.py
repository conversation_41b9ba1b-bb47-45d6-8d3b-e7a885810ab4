#!/usr/bin/env python3
"""
机器学习模型贝叶斯优化模块
支持多种算法的超参数优化
"""

import numpy as np
import pandas as pd
import optuna
from sklearn.model_selection import cross_val_score, StratifiedKFold, KFold
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.ensemble import GradientBoostingRegressor, GradientBoostingClassifier
from sklearn.svm import SVR, SVC
from sklearn.neural_network import M<PERSON>Regressor, MLPClassifier
from sklearn.linear_model import ElasticNet, Ridge
from sklearn.preprocessing import StandardScaler
import xgboost as xgb
import logging
import time
from typing import Dict, Any, Tuple, Optional

# 导入深度学习模块
try:
    from bp_neural_network import BPNeuralNetworkOptimizer
    from temporal_convolutional_network import TCNOptimizer
    DEEP_LEARNING_AVAILABLE = True
except ImportError:
    DEEP_LEARNING_AVAILABLE = False

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BayesianOptimizer:
    """贝叶斯优化器类"""
    
    def __init__(self, task_type: str, cv_folds: int = 5, n_trials: int = 100, random_state: int = 42):
        """
        初始化优化器
        
        Args:
            task_type: 'regression' 或 'classification'
            cv_folds: 交叉验证折数
            n_trials: 优化试验次数
            random_state: 随机种子
        """
        self.task_type = task_type
        self.cv_folds = cv_folds
        self.n_trials = n_trials
        self.random_state = random_state
        self.best_params = {}
        self.best_scores = {}
        self.optimization_history = {}
        
        # 设置交叉验证策略
        if task_type == 'regression':
            self.cv = KFold(n_splits=cv_folds, shuffle=True, random_state=random_state)
            self.scoring = 'r2'
        else:
            self.cv = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=random_state)
            self.scoring = 'accuracy'
    
    def optimize_random_forest(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """优化Random Forest参数"""
        logger.info("开始优化Random Forest参数...")
        
        def objective(trial):
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 100, 500),
                'max_depth': trial.suggest_int('max_depth', 10, 30),
                'min_samples_split': trial.suggest_int('min_samples_split', 2, 15),
                'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 8),
                'max_features': trial.suggest_categorical('max_features', ['sqrt', 'log2', 0.3, 0.5]),
                'random_state': self.random_state,
                'n_jobs': -1
            }
            
            if self.task_type == 'regression':
                model = RandomForestRegressor(**params)
            else:
                model = RandomForestClassifier(**params)
            
            scores = cross_val_score(model, X, y, cv=self.cv, scoring=self.scoring)
            return scores.mean()
        
        study = optuna.create_study(direction='maximize', sampler=optuna.samplers.TPESampler(seed=self.random_state))
        study.optimize(objective, n_trials=self.n_trials)
        
        self.best_params['RandomForest'] = study.best_params
        self.best_scores['RandomForest'] = study.best_value
        self.optimization_history['RandomForest'] = study.trials
        
        logger.info(f"Random Forest最佳参数: {study.best_params}")
        logger.info(f"Random Forest最佳分数: {study.best_value:.4f}")
        
        return study.best_params
    
    def optimize_svr(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """优化SVR参数"""
        logger.info("开始优化SVR参数...")

        def objective(trial):
            kernel = trial.suggest_categorical('kernel', ['rbf', 'poly', 'sigmoid'])
            params = {
                'kernel': kernel,
                'C': trial.suggest_float('C', 0.1, 100, log=True),
                'epsilon': trial.suggest_float('epsilon', 0.01, 0.5),
            }

            if kernel == 'poly':
                params['degree'] = trial.suggest_int('degree', 2, 5)
                params['gamma'] = trial.suggest_float('gamma_poly', 0.001, 1, log=True)
            elif kernel in ['rbf', 'sigmoid']:
                gamma_choice = trial.suggest_categorical('gamma_choice', ['scale', 'auto', 'custom'])
                if gamma_choice == 'custom':
                    params['gamma'] = trial.suggest_float('gamma_custom', 0.001, 1, log=True)
                else:
                    params['gamma'] = gamma_choice

            if self.task_type == 'regression':
                model = SVR(**params)
            else:
                model = SVC(**params, probability=True, random_state=self.random_state)

            # 标准化特征
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)

            scores = cross_val_score(model, X_scaled, y, cv=self.cv, scoring=self.scoring)
            return scores.mean()

        study = optuna.create_study(direction='maximize', sampler=optuna.samplers.TPESampler(seed=self.random_state))
        study.optimize(objective, n_trials=self.n_trials)

        self.best_params['SVR'] = study.best_params
        self.best_scores['SVR'] = study.best_value
        self.optimization_history['SVR'] = study.trials

        logger.info(f"SVR最佳参数: {study.best_params}")
        logger.info(f"SVR最佳分数: {study.best_value:.4f}")

        return study.best_params
    
    def optimize_gradient_boosting(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """优化Gradient Boosting参数"""
        logger.info("开始优化Gradient Boosting参数...")
        
        def objective(trial):
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 100, 500),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                'max_depth': trial.suggest_int('max_depth', 3, 10),
                'min_samples_split': trial.suggest_int('min_samples_split', 2, 20),
                'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 10),
                'subsample': trial.suggest_float('subsample', 0.8, 1.0),
                'random_state': self.random_state
            }
            
            if self.task_type == 'regression':
                model = GradientBoostingRegressor(**params)
            else:
                model = GradientBoostingClassifier(**params)
            
            scores = cross_val_score(model, X, y, cv=self.cv, scoring=self.scoring)
            return scores.mean()
        
        study = optuna.create_study(direction='maximize', sampler=optuna.samplers.TPESampler(seed=self.random_state))
        study.optimize(objective, n_trials=self.n_trials)
        
        self.best_params['GradientBoosting'] = study.best_params
        self.best_scores['GradientBoosting'] = study.best_value
        self.optimization_history['GradientBoosting'] = study.trials
        
        logger.info(f"Gradient Boosting最佳参数: {study.best_params}")
        logger.info(f"Gradient Boosting最佳分数: {study.best_value:.4f}")
        
        return study.best_params
    
    def optimize_xgboost(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """优化XGBoost参数"""
        logger.info("开始优化XGBoost参数...")
        
        def objective(trial):
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 100, 500),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                'max_depth': trial.suggest_int('max_depth', 3, 10),
                'min_child_weight': trial.suggest_int('min_child_weight', 1, 10),
                'subsample': trial.suggest_float('subsample', 0.8, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.8, 1.0),
                'reg_alpha': trial.suggest_float('reg_alpha', 0, 1),
                'reg_lambda': trial.suggest_float('reg_lambda', 0, 1),
                'random_state': self.random_state
            }
            
            if self.task_type == 'regression':
                model = xgb.XGBRegressor(**params, eval_metric='rmse')
            else:
                model = xgb.XGBClassifier(**params, eval_metric='logloss')
            
            scores = cross_val_score(model, X, y, cv=self.cv, scoring=self.scoring)
            return scores.mean()
        
        study = optuna.create_study(direction='maximize', sampler=optuna.samplers.TPESampler(seed=self.random_state))
        study.optimize(objective, n_trials=self.n_trials)
        
        self.best_params['XGBoost'] = study.best_params
        self.best_scores['XGBoost'] = study.best_value
        self.optimization_history['XGBoost'] = study.trials
        
        logger.info(f"XGBoost最佳参数: {study.best_params}")
        logger.info(f"XGBoost最佳分数: {study.best_value:.4f}")
        
        return study.best_params
    
    def optimize_mlp(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """优化MLP参数"""
        logger.info("开始优化MLP参数...")
        
        def objective(trial):
            # 网络结构
            n_layers = trial.suggest_int('n_layers', 2, 4)
            hidden_layer_sizes = []
            for i in range(n_layers):
                size = trial.suggest_int(f'layer_{i}_size', 64, 512)
                hidden_layer_sizes.append(size)
            
            params = {
                'hidden_layer_sizes': tuple(hidden_layer_sizes),
                'activation': trial.suggest_categorical('activation', ['relu', 'tanh']),
                'alpha': trial.suggest_float('alpha', 0.0001, 0.01, log=True),
                'learning_rate': trial.suggest_categorical('learning_rate', ['constant', 'adaptive']),
                'learning_rate_init': trial.suggest_float('learning_rate_init', 0.001, 0.01),
                'max_iter': 500,
                'random_state': self.random_state,
                'early_stopping': True,
                'validation_fraction': 0.1
            }
            
            if self.task_type == 'regression':
                model = MLPRegressor(**params)
            else:
                model = MLPClassifier(**params)
            
            # 标准化特征
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)
            
            scores = cross_val_score(model, X_scaled, y, cv=self.cv, scoring=self.scoring)
            return scores.mean()
        
        study = optuna.create_study(direction='maximize', sampler=optuna.samplers.TPESampler(seed=self.random_state))
        study.optimize(objective, n_trials=self.n_trials)
        
        self.best_params['MLP'] = study.best_params
        self.best_scores['MLP'] = study.best_value
        self.optimization_history['MLP'] = study.trials
        
        logger.info(f"MLP最佳参数: {study.best_params}")
        logger.info(f"MLP最佳分数: {study.best_value:.4f}")
        
        return study.best_params

    def optimize_bp_neural_network(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """优化BP神经网络参数"""
        if not DEEP_LEARNING_AVAILABLE:
            logger.warning("深度学习模块不可用，跳过BP神经网络优化")
            return {}

        logger.info("开始优化BP神经网络参数...")

        try:
            optimizer = BPNeuralNetworkOptimizer(
                task_type=self.task_type,
                n_trials=min(self.n_trials, 30),  # 限制试验次数以节省时间
                cv_folds=self.cv_folds
            )

            results = optimizer.optimize(X, y)

            self.best_params['BPNeuralNetwork'] = results['best_params']
            self.best_scores['BPNeuralNetwork'] = results['best_score']
            self.optimization_history['BPNeuralNetwork'] = results['study'].trials

            logger.info(f"BP神经网络最佳参数: {results['best_params']}")
            logger.info(f"BP神经网络最佳分数: {results['best_score']:.4f}")

            return results['best_params']

        except Exception as e:
            logger.error(f"BP神经网络优化失败: {str(e)}")
            return {}

    def optimize_tcn(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """优化TCN参数"""
        if not DEEP_LEARNING_AVAILABLE:
            logger.warning("深度学习模块不可用，跳过TCN优化")
            return {}

        logger.info("开始优化TCN参数...")

        try:
            optimizer = TCNOptimizer(
                task_type=self.task_type,
                n_trials=min(self.n_trials, 20),  # TCN训练较慢，减少试验次数
                cv_folds=self.cv_folds
            )

            results = optimizer.optimize(X, y)

            self.best_params['TCN'] = results['best_params']
            self.best_scores['TCN'] = results['best_score']
            self.optimization_history['TCN'] = results['study'].trials

            logger.info(f"TCN最佳参数: {results['best_params']}")
            logger.info(f"TCN最佳分数: {results['best_score']:.4f}")

            return results['best_params']

        except Exception as e:
            logger.error(f"TCN优化失败: {str(e)}")
            return {}

    def optimize_all_models(self, X: np.ndarray, y: np.ndarray, include_deep_learning: bool = True) -> Dict[str, Dict[str, Any]]:
        """优化所有模型"""
        logger.info(f"开始优化所有模型 (任务类型: {self.task_type})")
        start_time = time.time()

        # 优化传统机器学习模型
        self.optimize_random_forest(X, y)
        self.optimize_gradient_boosting(X, y)
        self.optimize_xgboost(X, y)
        self.optimize_svr(X, y)
        self.optimize_mlp(X, y)

        # 优化深度学习模型
        if include_deep_learning and DEEP_LEARNING_AVAILABLE:
            logger.info("开始优化深度学习模型...")
            self.optimize_bp_neural_network(X, y)
            self.optimize_tcn(X, y)

        end_time = time.time()
        logger.info(f"所有模型优化完成，耗时: {end_time - start_time:.2f}秒")

        # 返回最佳参数
        return {
            'best_params': self.best_params,
            'best_scores': self.best_scores,
            'optimization_history': self.optimization_history
        }
    
    def get_best_model_name(self) -> str:
        """获取最佳模型名称"""
        if not self.best_scores:
            return None
        return max(self.best_scores.keys(), key=lambda k: self.best_scores[k])
    
    def save_optimization_results(self, filepath: str):
        """保存优化结果"""
        results = {
            'task_type': self.task_type,
            'cv_folds': self.cv_folds,
            'n_trials': self.n_trials,
            'best_params': self.best_params,
            'best_scores': self.best_scores,
            'best_model': self.get_best_model_name()
        }
        
        import json
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"优化结果已保存到: {filepath}")
