#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sensor Waveform Analyzer for Vibration Signal Analysis System
Generates time-series waveform plots and 30 time-frequency feature visualizations

Author: AI Assistant
Version: 1.0
Date: 2024-12-07
"""

import os
import sys
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from scipy import signal
from scipy.fft import fft, fftfreq
import pywt
import warnings
warnings.filterwarnings('ignore')

class SensorWaveformAnalyzer:
    """Sensor waveform and feature visualization analyzer"""
    
    def __init__(self, output_dir: str = "unified_charts", file_prefix: str = "waveform_analysis_"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.file_prefix = file_prefix
        
        # Setup academic style
        self.setup_academic_style()
        
        # Analysis parameters
        self.sampling_rate = 1000  # Hz
        self.sensor_data = None
        self.sensor_id = None
        self.time_vector = None
        self.features = None
        
    def setup_academic_style(self):
        """Setup academic publication style with Times New Roman font"""
        plt.rcParams['font.family'] = 'serif'
        plt.rcParams['font.serif'] = ['Times New Roman', 'Times', 'DejaVu Serif']
        plt.rcParams['mathtext.fontset'] = 'stix'
        plt.rcParams['axes.unicode_minus'] = False
        plt.rcParams['figure.dpi'] = 330
        plt.rcParams['savefig.dpi'] = 330
        plt.rcParams['font.size'] = 12
        plt.rcParams['axes.titlesize'] = 16
        plt.rcParams['axes.labelsize'] = 14
        plt.rcParams['xtick.labelsize'] = 12
        plt.rcParams['ytick.labelsize'] = 12
        plt.rcParams['legend.fontsize'] = 12
        plt.rcParams['figure.titlesize'] = 18
        
        # IEEE/Elsevier color scheme
        self.colors = {
            'primary': '#1f77b4', 'secondary': '#ff7f0e', 'success': '#2ca02c',
            'danger': '#d62728', 'warning': '#ff7f0e', 'info': '#17a2b8'
        }
    
    def load_sensor_waveform_data(self, file_path: str, sensor_id: str):
        """Load sensor waveform data from merged_data.csv file"""
        try:
            print(f"📂 Loading waveform data from: {file_path}")
            
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"Data file not found: {file_path}")
            
            # Read CSV file
            df = pd.read_csv(file_path)
            print(f"   📊 Data shape: {df.shape}")
            print(f"   📋 Columns: {list(df.columns)}")
            
            # Check if sensor column exists
            if sensor_id not in df.columns:
                available_sensors = [col for col in df.columns if 'sensor' in col.lower() or 'Sensor' in col]
                raise ValueError(f"Sensor {sensor_id} not found. Available sensors: {available_sensors}")
            
            # Extract sensor data
            sensor_data = df[sensor_id].values
            
            # Create time vector
            time_vector = np.arange(len(sensor_data)) / self.sampling_rate
            
            # Store data
            self.sensor_data = sensor_data
            self.sensor_id = sensor_id
            self.time_vector = time_vector
            
            print(f"   ✅ Successfully loaded {len(sensor_data)} data points for {sensor_id}")
            print(f"   📈 Data range: {np.min(sensor_data):.4f} to {np.max(sensor_data):.4f} mg")
            print(f"   ⏱️ Time duration: {time_vector[-1]:.2f} seconds")
            
            return True
            
        except Exception as e:
            print(f"   ❌ Error loading sensor waveform data: {str(e)}")
            return False
    
    def extract_30_features(self):
        """Extract 30 time-frequency domain features from sensor data"""
        if self.sensor_data is None:
            print("❌ No sensor data available for feature extraction")
            return False
        
        print(f"🔧 Extracting 30 time-frequency features from {self.sensor_id}...")
        
        try:
            features = {}
            
            # Time domain features (10 features)
            features['mean'] = np.mean(self.sensor_data)
            features['std'] = np.std(self.sensor_data)
            features['var'] = np.var(self.sensor_data)
            features['rms'] = np.sqrt(np.mean(self.sensor_data**2))
            features['peak'] = np.max(np.abs(self.sensor_data))
            features['peak_to_peak'] = np.max(self.sensor_data) - np.min(self.sensor_data)
            features['crest_factor'] = features['peak'] / features['rms'] if features['rms'] > 0 else 0
            features['skewness'] = float(pd.Series(self.sensor_data).skew())
            features['kurtosis'] = float(pd.Series(self.sensor_data).kurtosis())
            features['energy'] = np.sum(self.sensor_data**2)
            
            # Frequency domain features (10 features)
            # FFT analysis
            fft_values = fft(self.sensor_data)
            fft_freqs = fftfreq(len(self.sensor_data), 1/self.sampling_rate)
            positive_freq_idx = fft_freqs > 0
            freqs = fft_freqs[positive_freq_idx]
            magnitude = np.abs(fft_values[positive_freq_idx])
            
            # Zero crossing rate
            zero_crossings = np.where(np.diff(np.signbit(self.sensor_data)))[0]
            features['zero_crossing_rate'] = len(zero_crossings) / len(self.sensor_data)
            
            # Dominant frequency
            dominant_freq_idx = np.argmax(magnitude)
            features['dominant_frequency'] = freqs[dominant_freq_idx]
            
            # Mean frequency
            features['mean_frequency'] = np.sum(freqs * magnitude) / np.sum(magnitude)
            
            # Frequency standard deviation
            features['frequency_std'] = np.sqrt(np.sum(((freqs - features['mean_frequency'])**2) * magnitude) / np.sum(magnitude))
            
            # Spectral centroid
            features['spectral_centroid'] = features['mean_frequency']
            
            # Spectral rolloff (85% of energy)
            cumulative_magnitude = np.cumsum(magnitude)
            rolloff_threshold = 0.85 * cumulative_magnitude[-1]
            rolloff_idx = np.where(cumulative_magnitude >= rolloff_threshold)[0]
            features['spectral_rolloff'] = freqs[rolloff_idx[0]] if len(rolloff_idx) > 0 else freqs[-1]
            
            # Spectral flux
            if len(magnitude) > 1:
                spectral_flux = np.sum(np.diff(magnitude)**2)
                features['spectral_flux'] = spectral_flux
            else:
                features['spectral_flux'] = 0
            
            # Power in different frequency bands
            total_power = np.sum(magnitude**2)
            low_freq_mask = (freqs >= 1) & (freqs <= 50)
            mid_freq_mask = (freqs >= 50) & (freqs <= 150)
            high_freq_mask = (freqs >= 150) & (freqs <= 400)
            
            features['total_power'] = total_power
            features['low_freq_power'] = np.sum(magnitude[low_freq_mask]**2) / total_power if total_power > 0 else 0
            features['mid_freq_power'] = np.sum(magnitude[mid_freq_mask]**2) / total_power if total_power > 0 else 0
            features['high_freq_power'] = np.sum(magnitude[high_freq_mask]**2) / total_power if total_power > 0 else 0
            
            # Time-frequency domain features (10 features)
            # Spectrogram analysis
            f_spec, t_spec, Sxx = signal.spectrogram(self.sensor_data, fs=self.sampling_rate, 
                                                    nperseg=min(256, len(self.sensor_data)//4))
            
            features['spectrogram_mean'] = np.mean(Sxx)
            features['spectrogram_std'] = np.std(Sxx)
            features['spectrogram_max'] = np.max(Sxx)
            
            # Time-bandwidth product
            signal_duration = len(self.sensor_data) / self.sampling_rate
            bandwidth = features['spectral_rolloff'] - 1  # Approximate bandwidth
            features['time_bandwidth_product'] = signal_duration * bandwidth
            
            # Wavelet transform features
            wavelet = 'db4'
            coeffs = pywt.wavedec(self.sensor_data, wavelet, level=4)
            
            # Wavelet energy in different levels
            features['wavelet_energy_d1'] = np.sum(coeffs[1]**2) / features['energy'] if features['energy'] > 0 else 0
            features['wavelet_energy_d2'] = np.sum(coeffs[2]**2) / features['energy'] if features['energy'] > 0 else 0
            features['wavelet_energy_d3'] = np.sum(coeffs[3]**2) / features['energy'] if features['energy'] > 0 else 0
            features['wavelet_energy_d4'] = np.sum(coeffs[4]**2) / features['energy'] if features['energy'] > 0 else 0
            features['wavelet_energy_a4'] = np.sum(coeffs[0]**2) / features['energy'] if features['energy'] > 0 else 0
            
            # Remove the extra feature to keep exactly 30 features
            # features['instantaneous_frequency_mean'] = features['mean_frequency']  # Removed to maintain 30 features
            
            self.features = features
            
            print(f"   ✅ Successfully extracted 30 features")
            print(f"   📊 Feature categories: 10 time + 10 frequency + 10 time-frequency")
            
            return True
            
        except Exception as e:
            print(f"   ❌ Error extracting features: {str(e)}")
            return False
    
    def generate_waveform_plot(self):
        """Generate time-series waveform plot with key moment identification"""
        if self.sensor_data is None or self.time_vector is None:
            print("❌ No sensor data available for waveform plotting")
            return False
        
        print(f"📈 Generating waveform plot for {self.sensor_id}...")
        
        try:
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))
            fig.suptitle(f'Vehicle Passage Vibration Waveform Analysis - {self.sensor_id}', 
                        fontsize=18, fontweight='bold', y=0.95)
            
            # Main waveform plot
            ax1.plot(self.time_vector, self.sensor_data, color=self.colors['primary'], 
                    linewidth=1.2, alpha=0.8, label='Vibration Signal')
            
            # Identify and mark key moments
            # Find peaks (vehicle axles)
            peaks, properties = signal.find_peaks(np.abs(self.sensor_data), 
                                                 height=np.std(self.sensor_data)*2, 
                                                 distance=int(0.1*self.sampling_rate))  # Min 0.1s between peaks
            
            # Mark peaks
            if len(peaks) > 0:
                ax1.scatter(self.time_vector[peaks], self.sensor_data[peaks], 
                           color=self.colors['danger'], s=80, zorder=5, 
                           label=f'Vehicle Axles ({len(peaks)} detected)')
                
                # Annotate peaks
                for i, peak_idx in enumerate(peaks):
                    ax1.annotate(f'Axle {i+1}', 
                               xy=(self.time_vector[peak_idx], self.sensor_data[peak_idx]),
                               xytext=(10, 10), textcoords='offset points',
                               fontsize=10, ha='left',
                               bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
                               arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
            
            # Mark vehicle passage start and end (based on signal energy)
            signal_energy = np.convolve(self.sensor_data**2, np.ones(50)/50, mode='same')
            energy_threshold = np.mean(signal_energy) + 2*np.std(signal_energy)
            passage_indices = np.where(signal_energy > energy_threshold)[0]
            
            if len(passage_indices) > 0:
                passage_start = self.time_vector[passage_indices[0]]
                passage_end = self.time_vector[passage_indices[-1]]
                
                ax1.axvline(x=passage_start, color=self.colors['success'], linestyle='--', 
                           alpha=0.7, label='Vehicle Entry')
                ax1.axvline(x=passage_end, color=self.colors['warning'], linestyle='--', 
                           alpha=0.7, label='Vehicle Exit')
                
                # Shade vehicle passage period
                ax1.axvspan(passage_start, passage_end, alpha=0.1, color=self.colors['info'], 
                           label='Vehicle Passage Period')
            
            ax1.set_title('Vehicle Passage Vibration Waveform', fontsize=16, pad=20)
            ax1.set_xlabel('Time (s)', fontsize=14)
            ax1.set_ylabel('Amplitude (mg)', fontsize=14)
            ax1.grid(True, alpha=0.3)
            ax1.legend(loc='upper right')
            
            # Add statistics box
            stats_text = (f'Duration: {self.time_vector[-1]:.2f}s\n'
                         f'Peak: {np.max(np.abs(self.sensor_data)):.2f} mg\n'
                         f'RMS: {np.sqrt(np.mean(self.sensor_data**2)):.2f} mg\n'
                         f'Axles Detected: {len(peaks)}')
            ax1.text(0.02, 0.98, stats_text, transform=ax1.transAxes, fontsize=11,
                    verticalalignment='top', bbox=dict(boxstyle="round,pad=0.4", 
                    facecolor='white', alpha=0.8))
            
            # Detailed view of main vehicle passage
            if len(passage_indices) > 0:
                # Focus on vehicle passage period with some margin
                margin = int(0.1 * self.sampling_rate)  # 0.1s margin
                start_idx = max(0, passage_indices[0] - margin)
                end_idx = min(len(self.sensor_data), passage_indices[-1] + margin)
                
                detail_time = self.time_vector[start_idx:end_idx]
                detail_data = self.sensor_data[start_idx:end_idx]
                
                ax2.plot(detail_time, detail_data, color=self.colors['primary'], 
                        linewidth=1.5, alpha=0.9)
                
                # Mark peaks in detailed view
                detail_peaks = peaks[(peaks >= start_idx) & (peaks < end_idx)]
                if len(detail_peaks) > 0:
                    ax2.scatter(self.time_vector[detail_peaks], self.sensor_data[detail_peaks], 
                               color=self.colors['danger'], s=100, zorder=5)
                
                ax2.set_title('Detailed View - Vehicle Passage Period', fontsize=16, pad=20)
                ax2.set_xlabel('Time (s)', fontsize=14)
                ax2.set_ylabel('Amplitude (mg)', fontsize=14)
                ax2.grid(True, alpha=0.3)
                
                # Add envelope
                from scipy.signal import hilbert
                analytic_signal = hilbert(detail_data)
                envelope = np.abs(analytic_signal)
                ax2.plot(detail_time, envelope, color=self.colors['warning'], 
                        linewidth=2, alpha=0.7, linestyle='--', label='Signal Envelope')
                ax2.plot(detail_time, -envelope, color=self.colors['warning'], 
                        linewidth=2, alpha=0.7, linestyle='--')
                ax2.legend()
            else:
                # If no clear passage detected, show full signal
                ax2.plot(self.time_vector, self.sensor_data, color=self.colors['secondary'], 
                        linewidth=1.0, alpha=0.8)
                ax2.set_title('Full Signal View', fontsize=16, pad=20)
                ax2.set_xlabel('Time (s)', fontsize=14)
                ax2.set_ylabel('Amplitude (mg)', fontsize=14)
                ax2.grid(True, alpha=0.3)
            
            plt.tight_layout()
            plt.subplots_adjust(top=0.92)
            
            # Save plot
            plot_path = self.output_dir / f'{self.file_prefix}{self.sensor_id}_waveform.png'
            plt.savefig(plot_path, dpi=330, bbox_inches='tight', facecolor='white')
            plt.close()
            
            print(f"   ✅ Waveform plot saved: {plot_path}")
            return True
            
        except Exception as e:
            print(f"   ❌ Error generating waveform plot: {str(e)}")
            return False

    def generate_features_visualization(self):
        """Generate 30 time-frequency features visualization"""
        if self.features is None:
            print("❌ No features available for visualization")
            return False

        print(f"📊 Generating 30 features visualization for {self.sensor_id}...")

        try:
            fig = plt.figure(figsize=(20, 16))
            fig.suptitle(f'30 Time-Frequency Domain Features Analysis - {self.sensor_id}',
                        fontsize=20, fontweight='bold', y=0.95)

            # Organize features by category
            time_features = {
                'Mean': self.features['mean'],
                'Std Dev': self.features['std'],
                'Variance': self.features['var'],
                'RMS': self.features['rms'],
                'Peak': self.features['peak'],
                'Peak-to-Peak': self.features['peak_to_peak'],
                'Crest Factor': self.features['crest_factor'],
                'Skewness': self.features['skewness'],
                'Kurtosis': self.features['kurtosis'],
                'Energy': self.features['energy']
            }

            freq_features = {
                'Zero Cross Rate': self.features['zero_crossing_rate'],
                'Dominant Freq': self.features['dominant_frequency'],
                'Mean Freq': self.features['mean_frequency'],
                'Freq Std': self.features['frequency_std'],
                'Spectral Centroid': self.features['spectral_centroid'],
                'Spectral Rolloff': self.features['spectral_rolloff'],
                'Spectral Flux': self.features['spectral_flux'],
                'Total Power': self.features['total_power'],
                'Low Freq Power': self.features['low_freq_power'],
                'Mid Freq Power': self.features['mid_freq_power']
            }

            time_freq_features = {
                'High Freq Power': self.features['high_freq_power'],
                'Spectrogram Mean': self.features['spectrogram_mean'],
                'Spectrogram Std': self.features['spectrogram_std'],
                'Spectrogram Max': self.features['spectrogram_max'],
                'Time-BW Product': self.features['time_bandwidth_product'],
                'Wavelet Energy D1': self.features['wavelet_energy_d1'],
                'Wavelet Energy D2': self.features['wavelet_energy_d2'],
                'Wavelet Energy D3': self.features['wavelet_energy_d3'],
                'Wavelet Energy D4': self.features['wavelet_energy_d4'],
                'Wavelet Energy A4': self.features['wavelet_energy_a4']
            }

            # Create subplots
            gs = fig.add_gridspec(3, 4, hspace=0.3, wspace=0.3)

            # 1. Time domain features bar chart
            ax1 = fig.add_subplot(gs[0, :2])
            time_names = list(time_features.keys())
            time_values = list(time_features.values())

            # Normalize values for better visualization
            time_values_norm = np.array(time_values)
            time_values_norm = (time_values_norm - np.min(time_values_norm)) / (np.max(time_values_norm) - np.min(time_values_norm) + 1e-8)

            bars1 = ax1.bar(range(len(time_names)), time_values_norm,
                           color=plt.cm.Blues(np.linspace(0.4, 0.8, len(time_names))), alpha=0.8)
            ax1.set_title('Time Domain Features (Normalized)', fontsize=14, pad=15)
            ax1.set_xlabel('Feature Type', fontsize=12)
            ax1.set_ylabel('Normalized Value', fontsize=12)
            ax1.set_xticks(range(len(time_names)))
            ax1.set_xticklabels(time_names, rotation=45, ha='right', fontsize=10)
            ax1.grid(True, alpha=0.3)

            # Add actual values as text
            for i, (bar, val) in enumerate(zip(bars1, time_values)):
                ax1.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.02,
                        f'{val:.2e}' if abs(val) > 1000 or abs(val) < 0.01 else f'{val:.3f}',
                        ha='center', va='bottom', fontsize=8, rotation=90)

            # 2. Frequency domain features bar chart
            ax2 = fig.add_subplot(gs[0, 2:])
            freq_names = list(freq_features.keys())
            freq_values = list(freq_features.values())

            # Normalize values for better visualization
            freq_values_norm = np.array(freq_values)
            freq_values_norm = (freq_values_norm - np.min(freq_values_norm)) / (np.max(freq_values_norm) - np.min(freq_values_norm) + 1e-8)

            bars2 = ax2.bar(range(len(freq_names)), freq_values_norm,
                           color=plt.cm.Oranges(np.linspace(0.4, 0.8, len(freq_names))), alpha=0.8)
            ax2.set_title('Frequency Domain Features (Normalized)', fontsize=14, pad=15)
            ax2.set_xlabel('Feature Type', fontsize=12)
            ax2.set_ylabel('Normalized Value', fontsize=12)
            ax2.set_xticks(range(len(freq_names)))
            ax2.set_xticklabels(freq_names, rotation=45, ha='right', fontsize=10)
            ax2.grid(True, alpha=0.3)

            # Add actual values as text
            for i, (bar, val) in enumerate(zip(bars2, freq_values)):
                ax2.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.02,
                        f'{val:.2e}' if abs(val) > 1000 or abs(val) < 0.01 else f'{val:.3f}',
                        ha='center', va='bottom', fontsize=8, rotation=90)

            # 3. Time-frequency domain features bar chart
            ax3 = fig.add_subplot(gs[1, :2])
            tf_names = list(time_freq_features.keys())
            tf_values = list(time_freq_features.values())

            # Normalize values for better visualization
            tf_values_norm = np.array(tf_values)
            tf_values_norm = (tf_values_norm - np.min(tf_values_norm)) / (np.max(tf_values_norm) - np.min(tf_values_norm) + 1e-8)

            bars3 = ax3.bar(range(len(tf_names)), tf_values_norm,
                           color=plt.cm.Greens(np.linspace(0.4, 0.8, len(tf_names))), alpha=0.8)
            ax3.set_title('Time-Frequency Domain Features (Normalized)', fontsize=14, pad=15)
            ax3.set_xlabel('Feature Type', fontsize=12)
            ax3.set_ylabel('Normalized Value', fontsize=12)
            ax3.set_xticks(range(len(tf_names)))
            ax3.set_xticklabels(tf_names, rotation=45, ha='right', fontsize=10)
            ax3.grid(True, alpha=0.3)

            # Add actual values as text
            for i, (bar, val) in enumerate(zip(bars3, tf_values)):
                ax3.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.02,
                        f'{val:.2e}' if abs(val) > 1000 or abs(val) < 0.01 else f'{val:.3f}',
                        ha='center', va='bottom', fontsize=8, rotation=90)

            # 4. Radar chart for key features
            ax4 = fig.add_subplot(gs[1, 2:], projection='polar')

            # Select key features for radar chart
            key_features = {
                'RMS': self.features['rms'],
                'Peak': self.features['peak'],
                'Crest Factor': self.features['crest_factor'],
                'Dominant Freq': self.features['dominant_frequency'],
                'Spectral Centroid': self.features['spectral_centroid'],
                'Total Power': self.features['total_power'],
                'Wavelet Energy D1': self.features['wavelet_energy_d1'],
                'Wavelet Energy D2': self.features['wavelet_energy_d2']
            }

            # Normalize for radar chart
            key_values = np.array(list(key_features.values()))
            key_values_norm = (key_values - np.min(key_values)) / (np.max(key_values) - np.min(key_values) + 1e-8)

            # Create radar chart
            angles = np.linspace(0, 2 * np.pi, len(key_features), endpoint=False).tolist()
            key_values_norm = key_values_norm.tolist()
            angles += angles[:1]  # Complete the circle
            key_values_norm += key_values_norm[:1]

            ax4.plot(angles, key_values_norm, 'o-', linewidth=2, color=self.colors['primary'], alpha=0.8)
            ax4.fill(angles, key_values_norm, alpha=0.25, color=self.colors['primary'])
            ax4.set_xticks(angles[:-1])
            ax4.set_xticklabels(list(key_features.keys()), fontsize=10)
            ax4.set_ylim(0, 1)
            ax4.set_title('Key Features Radar Chart', fontsize=14, pad=20)
            ax4.grid(True, alpha=0.3)

            plt.tight_layout()
            plt.subplots_adjust(top=0.92)

            # Save plot
            plot_path = self.output_dir / f'{self.file_prefix}{self.sensor_id}_features.png'
            plt.savefig(plot_path, dpi=330, bbox_inches='tight', facecolor='white')
            plt.close()

            print(f"   ✅ Features visualization saved: {plot_path}")
            return True

        except Exception as e:
            print(f"   ❌ Error generating features visualization: {str(e)}")
            return False

    def analyze_sensor_waveform(self, file_path: str, sensor_id: str):
        """Complete sensor waveform analysis workflow"""
        print(f"🚀 Starting sensor waveform analysis for {sensor_id}")
        print("=" * 80)

        # Load waveform data
        if not self.load_sensor_waveform_data(file_path, sensor_id):
            return False

        # Extract 30 features
        if not self.extract_30_features():
            print("⚠️ Feature extraction failed, continuing with waveform analysis only...")

        # Generate visualizations
        results = {
            'waveform_plot': self.generate_waveform_plot(),
            'features_plot': self.generate_features_visualization() if self.features else False
        }

        # Summary
        print("\n" + "=" * 80)
        print("📊 Waveform Analysis Summary")
        print("=" * 80)

        success_count = sum(results.values())
        total_analyses = len(results)

        for analysis_type, success in results.items():
            status = "✅ Success" if success else "❌ Failed"
            print(f"   {status} - {analysis_type.replace('_', ' ').title()}")

        print(f"\n📈 Overall Success Rate: {success_count}/{total_analyses} ({success_count/total_analyses*100:.1f}%)")
        print(f"📁 Output Directory: {self.output_dir}")
        print(f"🔧 Sensor Analyzed: {sensor_id}")
        print(f"📊 Data Points: {len(self.sensor_data) if self.sensor_data is not None else 0}")
        print(f"⏱️ Duration: {self.time_vector[-1]:.2f} seconds" if self.time_vector is not None else "")

        if self.features:
            print(f"🔍 Features Extracted: 30 time-frequency domain features")
            print(f"   - Time Domain: 10 features")
            print(f"   - Frequency Domain: 10 features")
            print(f"   - Time-Frequency Domain: 10 features")

        return success_count > 0


def main():
    """Main function with command line interface"""
    parser = argparse.ArgumentParser(
        description='Sensor Waveform Analyzer for Vibration Signal Analysis',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python sensor_waveform_analyzer.py experiment_data.csv Sensor_01
  python sensor_waveform_analyzer.py 25吨_三轴_40.0kmh_实验1_merged_data.csv Sensor_05
  python sensor_waveform_analyzer.py data.csv Sensor_10 --output-dir my_charts
        """
    )

    parser.add_argument('file_path', help='Path to merged_data.csv file')
    parser.add_argument('sensor_id', help='Sensor ID (e.g., Sensor_01)')
    parser.add_argument('--output-dir', default='unified_charts',
                       help='Output directory for charts (default: unified_charts)')
    parser.add_argument('--prefix', default='waveform_analysis_',
                       help='File prefix for generated charts (default: waveform_analysis_)')

    args = parser.parse_args()

    # Validate inputs
    if not os.path.exists(args.file_path):
        print(f"❌ Error: Data file not found: {args.file_path}")
        sys.exit(1)

    # Create analyzer
    analyzer = SensorWaveformAnalyzer(output_dir=args.output_dir, file_prefix=args.prefix)

    # Run analysis
    success = analyzer.analyze_sensor_waveform(
        file_path=args.file_path,
        sensor_id=args.sensor_id
    )

    if success:
        print("\n🎉 Sensor waveform analysis completed successfully!")
        print(f"📁 Check output directory: {args.output_dir}")
        print(f"📊 Generated charts:")
        print(f"   - {args.prefix}{args.sensor_id}_waveform.png")
        if analyzer.features:
            print(f"   - {args.prefix}{args.sensor_id}_features.png")
        sys.exit(0)
    else:
        print("\n❌ Sensor waveform analysis failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
