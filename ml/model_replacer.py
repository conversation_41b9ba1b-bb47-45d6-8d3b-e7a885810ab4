#!/usr/bin/env python3
"""
线性模型替换模块
用更强大的非线性模型替换表现不佳的线性模型
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.ensemble import GradientBoostingRegressor, GradientBoostingClassifier
from sklearn.ensemble import ExtraTreesRegressor, ExtraTreesClassifier
from sklearn.neural_network import MLPRegressor, MLPClassifier
from sklearn.linear_model import ElasticNet, Ridge, LogisticRegression
from sklearn.preprocessing import PolynomialFeatures, StandardScaler
from sklearn.pipeline import Pipeline
from sklearn.model_selection import cross_val_score
import xgboost as xgb
import logging
from typing import Dict, Any, List, Tuple

logger = logging.getLogger(__name__)

class ModelReplacer:
    """模型替换器类"""
    
    def __init__(self, task_type: str, random_state: int = 42):
        """
        初始化模型替换器
        
        Args:
            task_type: 'regression' 或 'classification'
            random_state: 随机种子
        """
        self.task_type = task_type
        self.random_state = random_state
        self.replacement_models = {}
        self.baseline_models = {}
        self.performance_comparison = {}
        
    def create_enhanced_models(self, optimized_params: Dict[str, Dict] = None) -> Dict[str, Any]:
        """
        创建增强的非线性模型来替换线性模型
        
        Args:
            optimized_params: 优化后的参数字典
        """
        logger.info(f"创建增强的{self.task_type}模型...")
        
        if self.task_type == 'regression':
            return self._create_regression_models(optimized_params)
        else:
            return self._create_classification_models(optimized_params)
    
    def _create_regression_models(self, optimized_params: Dict = None) -> Dict[str, Any]:
        """创建回归模型"""
        models = {}
        
        # 1. 多项式回归 + Ridge正则化
        models['Polynomial_Ridge_2'] = Pipeline([
            ('poly', PolynomialFeatures(degree=2, include_bias=False)),
            ('scaler', StandardScaler()),
            ('ridge', Ridge(alpha=1.0, random_state=self.random_state))
        ])
        
        models['Polynomial_Ridge_3'] = Pipeline([
            ('poly', PolynomialFeatures(degree=3, include_bias=False)),
            ('scaler', StandardScaler()),
            ('ridge', Ridge(alpha=10.0, random_state=self.random_state))
        ])
        
        # 2. ElasticNet回归
        models['ElasticNet'] = Pipeline([
            ('scaler', StandardScaler()),
            ('elasticnet', ElasticNet(alpha=0.1, l1_ratio=0.5, random_state=self.random_state))
        ])
        
        # 3. Gradient Boosting (使用优化参数或默认参数)
        gb_params = optimized_params.get('GradientBoosting', {}) if optimized_params else {}
        gb_default = {
            'n_estimators': 200,
            'learning_rate': 0.1,
            'max_depth': 6,
            'min_samples_split': 5,
            'min_samples_leaf': 2,
            'subsample': 0.9,
            'random_state': self.random_state
        }
        gb_params = {**gb_default, **gb_params}
        models['GradientBoosting'] = GradientBoostingRegressor(**gb_params)
        
        # 4. XGBoost (使用优化参数或默认参数)
        xgb_params = optimized_params.get('XGBoost', {}) if optimized_params else {}
        xgb_default = {
            'n_estimators': 200,
            'learning_rate': 0.1,
            'max_depth': 6,
            'min_child_weight': 3,
            'subsample': 0.9,
            'colsample_bytree': 0.9,
            'reg_alpha': 0.1,
            'reg_lambda': 0.1,
            'random_state': self.random_state,
            'eval_metric': 'rmse'
        }
        xgb_params = {**xgb_default, **xgb_params}
        models['XGBoost'] = xgb.XGBRegressor(**xgb_params)
        
        # 5. Extra Trees
        models['ExtraTrees'] = ExtraTreesRegressor(
            n_estimators=200,
            max_depth=20,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=self.random_state,
            n_jobs=-1
        )
        
        # 6. 增强型MLP (使用优化参数或默认参数)
        mlp_params = optimized_params.get('MLP', {}) if optimized_params else {}
        mlp_default = {
            'hidden_layer_sizes': (256, 128, 64),
            'activation': 'relu',
            'alpha': 0.001,
            'learning_rate': 'adaptive',
            'learning_rate_init': 0.001,
            'max_iter': 500,
            'random_state': self.random_state,
            'early_stopping': True,
            'validation_fraction': 0.1
        }
        mlp_params = {**mlp_default, **mlp_params}
        models['Enhanced_MLP'] = Pipeline([
            ('scaler', StandardScaler()),
            ('mlp', MLPRegressor(**mlp_params))
        ])
        
        return models
    
    def _create_classification_models(self, optimized_params: Dict = None) -> Dict[str, Any]:
        """创建分类模型"""
        models = {}
        
        # 1. Gradient Boosting分类器
        gb_params = optimized_params.get('GradientBoosting', {}) if optimized_params else {}
        gb_default = {
            'n_estimators': 200,
            'learning_rate': 0.1,
            'max_depth': 6,
            'min_samples_split': 5,
            'min_samples_leaf': 2,
            'subsample': 0.9,
            'random_state': self.random_state
        }
        gb_params = {**gb_default, **gb_params}
        models['GradientBoosting'] = GradientBoostingClassifier(**gb_params)
        
        # 2. XGBoost分类器
        xgb_params = optimized_params.get('XGBoost', {}) if optimized_params else {}
        xgb_default = {
            'n_estimators': 200,
            'learning_rate': 0.1,
            'max_depth': 6,
            'min_child_weight': 3,
            'subsample': 0.9,
            'colsample_bytree': 0.9,
            'reg_alpha': 0.1,
            'reg_lambda': 0.1,
            'random_state': self.random_state,
            'eval_metric': 'logloss'
        }
        xgb_params = {**xgb_default, **xgb_params}
        models['XGBoost'] = xgb.XGBClassifier(**xgb_params)
        
        # 3. Extra Trees分类器
        models['ExtraTrees'] = ExtraTreesClassifier(
            n_estimators=200,
            max_depth=20,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=self.random_state,
            n_jobs=-1
        )
        
        # 4. 增强型MLP分类器
        mlp_params = optimized_params.get('MLP', {}) if optimized_params else {}
        mlp_default = {
            'hidden_layer_sizes': (256, 128, 64),
            'activation': 'relu',
            'alpha': 0.001,
            'learning_rate': 'adaptive',
            'learning_rate_init': 0.001,
            'max_iter': 500,
            'random_state': self.random_state,
            'early_stopping': True,
            'validation_fraction': 0.1
        }
        mlp_params = {**mlp_default, **mlp_params}
        models['Enhanced_MLP'] = Pipeline([
            ('scaler', StandardScaler()),
            ('mlp', MLPClassifier(**mlp_params))
        ])
        
        return models
    
    def create_baseline_models(self) -> Dict[str, Any]:
        """创建基线模型（包括原有的线性模型）"""
        logger.info("创建基线模型...")
        
        if self.task_type == 'regression':
            from sklearn.linear_model import LinearRegression
            from sklearn.svm import SVR
            
            models = {
                'Linear_Regression': LinearRegression(),
                'SVR_baseline': Pipeline([
                    ('scaler', StandardScaler()),
                    ('svr', SVR(kernel='rbf', C=1.0, gamma='scale', epsilon=0.1))
                ]),
                'Random_Forest_baseline': RandomForestRegressor(
                    n_estimators=100, 
                    random_state=self.random_state,
                    n_jobs=-1
                )
            }
        else:
            from sklearn.svm import SVC
            
            models = {
                'Logistic_Regression': Pipeline([
                    ('scaler', StandardScaler()),
                    ('lr', LogisticRegression(random_state=self.random_state, max_iter=1000))
                ]),
                'SVC_baseline': Pipeline([
                    ('scaler', StandardScaler()),
                    ('svc', SVC(kernel='rbf', C=1.0, gamma='scale', random_state=self.random_state, probability=True))
                ]),
                'Random_Forest_baseline': RandomForestClassifier(
                    n_estimators=100,
                    random_state=self.random_state,
                    n_jobs=-1
                )
            }
        
        return models
    
    def compare_models(self, X: np.ndarray, y: np.ndarray, cv_folds: int = 5) -> Dict[str, Dict]:
        """比较基线模型和替换模型的性能"""
        logger.info("开始模型性能比较...")
        
        from sklearn.model_selection import cross_val_score, StratifiedKFold, KFold
        
        # 设置交叉验证
        if self.task_type == 'regression':
            cv = KFold(n_splits=cv_folds, shuffle=True, random_state=self.random_state)
            scoring = 'r2'
        else:
            cv = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=self.random_state)
            scoring = 'accuracy'
        
        # 获取所有模型
        baseline_models = self.create_baseline_models()
        enhanced_models = self.create_enhanced_models()
        
        results = {
            'baseline': {},
            'enhanced': {}
        }
        
        # 评估基线模型
        logger.info("评估基线模型...")
        for name, model in baseline_models.items():
            try:
                scores = cross_val_score(model, X, y, cv=cv, scoring=scoring)
                results['baseline'][name] = {
                    'mean_score': scores.mean(),
                    'std_score': scores.std(),
                    'scores': scores.tolist()
                }
                logger.info(f"  {name}: {scores.mean():.4f} ± {scores.std():.4f}")
            except Exception as e:
                logger.error(f"  {name}: 评估失败 - {str(e)}")
                results['baseline'][name] = {'error': str(e)}
        
        # 评估增强模型
        logger.info("评估增强模型...")
        for name, model in enhanced_models.items():
            try:
                scores = cross_val_score(model, X, y, cv=cv, scoring=scoring)
                results['enhanced'][name] = {
                    'mean_score': scores.mean(),
                    'std_score': scores.std(),
                    'scores': scores.tolist()
                }
                logger.info(f"  {name}: {scores.mean():.4f} ± {scores.std():.4f}")
            except Exception as e:
                logger.error(f"  {name}: 评估失败 - {str(e)}")
                results['enhanced'][name] = {'error': str(e)}
        
        self.performance_comparison = results
        return results
    
    def get_best_replacement_models(self) -> List[Tuple[str, float]]:
        """获取最佳的替换模型"""
        if not self.performance_comparison:
            return []
        
        enhanced_results = self.performance_comparison.get('enhanced', {})
        valid_results = {k: v for k, v in enhanced_results.items() if 'error' not in v}
        
        # 按性能排序
        sorted_models = sorted(
            valid_results.items(),
            key=lambda x: x[1]['mean_score'],
            reverse=True
        )
        
        return [(name, result['mean_score']) for name, result in sorted_models]
    
    def save_comparison_results(self, filepath: str):
        """保存比较结果"""
        import json
        
        results = {
            'task_type': self.task_type,
            'performance_comparison': self.performance_comparison,
            'best_replacements': self.get_best_replacement_models()
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"模型比较结果已保存到: {filepath}")
