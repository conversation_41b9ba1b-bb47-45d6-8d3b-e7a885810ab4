#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的可视化基础类
解决图表布局重叠问题，优化配色方案，提供统一的可视化基础功能

Author: AI Assistant
Version: 1.0
Date: 2024-12-07
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import seaborn as sns
import os
import platform
import warnings
from pathlib import Path
warnings.filterwarnings('ignore')

class OptimizedVisualizationBase:
    """优化的可视化基础类"""
    
    def __init__(self, output_dir='unified_charts', file_prefix='optimized_'):
        """
        初始化优化的可视化基础类
        
        Args:
            output_dir: 输出目录
            file_prefix: 文件前缀
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.file_prefix = file_prefix
        
        # 应用优化的可视化配置
        self._apply_optimized_config()
        
        # 定义优化的配色方案
        self._define_color_schemes()
        
        # 定义布局参数
        self._define_layout_params()
    
    def _apply_optimized_config(self):
        """应用优化的可视化配置"""
        # 设置高质量渲染
        plt.rcParams['figure.dpi'] = 330
        plt.rcParams['savefig.dpi'] = 330
        plt.rcParams['savefig.format'] = 'png'
        plt.rcParams['savefig.bbox'] = 'tight'
        plt.rcParams['savefig.facecolor'] = 'white'
        plt.rcParams['savefig.edgecolor'] = 'none'
        
        # 设置字体配置（优先使用Times New Roman）
        plt.rcParams['font.family'] = 'serif'
        plt.rcParams['font.serif'] = ['Times New Roman', 'Times', 'DejaVu Serif']
        plt.rcParams['font.size'] = 12
        plt.rcParams['axes.titlesize'] = 16
        plt.rcParams['axes.labelsize'] = 14
        plt.rcParams['xtick.labelsize'] = 12
        plt.rcParams['ytick.labelsize'] = 12
        plt.rcParams['legend.fontsize'] = 12
        plt.rcParams['figure.titlesize'] = 18
        
        # 优化布局参数
        plt.rcParams['figure.autolayout'] = False  # 禁用自动布局，使用手动优化
        plt.rcParams['axes.grid'] = True
        plt.rcParams['grid.alpha'] = 0.3
        plt.rcParams['axes.axisbelow'] = True
        
        # 优化线条和标记
        plt.rcParams['lines.linewidth'] = 2.0
        plt.rcParams['lines.markersize'] = 6
        plt.rcParams['axes.linewidth'] = 1.2
        
        # 禁用中文字符的负号问题
        plt.rcParams['axes.unicode_minus'] = False
    
    def _define_color_schemes(self):
        """定义优化的配色方案"""
        # 高对比度配色方案
        self.color_schemes = {
            'high_contrast': {
                'before': '#1f77b4',      # 深蓝色 - 处理前
                'after': '#ff7f0e',       # 橙色 - 处理后
                'primary': '#2ca02c',     # 绿色 - 主要
                'secondary': '#d62728',   # 红色 - 次要
                'accent': '#9467bd',      # 紫色 - 强调
                'neutral': '#7f7f7f'      # 灰色 - 中性
            },
            'academic': {
                'blue': '#0072BD',
                'orange': '#D95319',
                'yellow': '#EDB120',
                'purple': '#7E2F8E',
                'green': '#77AC30',
                'cyan': '#4DBEEE',
                'red': '#A2142F'
            },
            'preprocessing': {
                'raw_data': '#1f77b4',        # 深蓝色 - 原始数据
                'processed_data': '#ff7f0e',  # 橙色 - 处理后数据
                'noise': '#d62728',           # 红色 - 噪声
                'signal': '#2ca02c',          # 绿色 - 信号
                'filtered': '#9467bd'         # 紫色 - 滤波后
            }
        }
        
        # 线型样式
        self.line_styles = {
            'solid': '-',
            'dashed': '--',
            'dotted': ':',
            'dashdot': '-.'
        }
        
        # 标记样式
        self.marker_styles = {
            'circle': 'o',
            'square': 's',
            'triangle': '^',
            'diamond': 'D',
            'star': '*',
            'plus': '+',
            'cross': 'x'
        }
    
    def _define_layout_params(self):
        """定义布局参数"""
        self.layout_params = {
            'single_plot': {
                'figsize': (12, 8),
                'left': 0.1,
                'right': 0.95,
                'top': 0.92,
                'bottom': 0.12,
                'hspace': 0.3,
                'wspace': 0.3
            },
            'subplot_2x2': {
                'figsize': (16, 12),
                'left': 0.08,
                'right': 0.95,
                'top': 0.92,
                'bottom': 0.08,
                'hspace': 0.4,
                'wspace': 0.3
            },
            'subplot_3x2': {
                'figsize': (18, 15),
                'left': 0.06,
                'right': 0.96,
                'top': 0.94,
                'bottom': 0.06,
                'hspace': 0.5,
                'wspace': 0.25
            },
            'large_comparison': {
                'figsize': (20, 16),
                'left': 0.05,
                'right': 0.97,
                'top': 0.95,
                'bottom': 0.05,
                'hspace': 0.6,
                'wspace': 0.3
            }
        }
    
    def create_optimized_figure(self, layout_type='single_plot', title=None):
        """
        创建优化的图形对象
        
        Args:
            layout_type: 布局类型
            title: 图形标题
            
        Returns:
            fig: 图形对象
        """
        params = self.layout_params[layout_type]
        fig = plt.figure(figsize=params['figsize'])
        
        if title:
            fig.suptitle(title, fontsize=18, fontweight='bold', y=0.96)
        
        return fig
    
    def apply_optimized_layout(self, fig, layout_type='single_plot'):
        """
        应用优化的布局
        
        Args:
            fig: 图形对象
            layout_type: 布局类型
        """
        params = self.layout_params[layout_type]
        
        # 应用tight_layout
        plt.tight_layout()
        
        # 应用自定义布局参数
        plt.subplots_adjust(
            left=params['left'],
            right=params['right'],
            top=params['top'],
            bottom=params['bottom'],
            hspace=params['hspace'],
            wspace=params['wspace']
        )
    
    def save_optimized_figure(self, fig, filename, close_fig=True):
        """
        保存优化的图形
        
        Args:
            fig: 图形对象
            filename: 文件名
            close_fig: 是否关闭图形
        """
        filepath = self.output_dir / f'{self.file_prefix}{filename}'
        
        # 确保目录存在
        filepath.parent.mkdir(parents=True, exist_ok=True)
        
        # 保存图形
        fig.savefig(
            filepath,
            dpi=330,
            bbox_inches='tight',
            facecolor='white',
            edgecolor='none',
            pad_inches=0.1
        )
        
        if close_fig:
            plt.close(fig)
        
        print(f"   ✅ Saved: {filepath}")
        return str(filepath)
    
    def create_legend_with_optimal_position(self, ax, labels, colors=None, 
                                          linestyles=None, markers=None, 
                                          position='best'):
        """
        创建位置优化的图例
        
        Args:
            ax: 轴对象
            labels: 标签列表
            colors: 颜色列表
            linestyles: 线型列表
            markers: 标记列表
            position: 图例位置
        """
        if colors is None:
            colors = [self.color_schemes['high_contrast']['primary']] * len(labels)
        
        if linestyles is None:
            linestyles = ['-'] * len(labels)
        
        if markers is None:
            markers = ['o'] * len(labels)
        
        # 创建图例元素
        legend_elements = []
        for i, label in enumerate(labels):
            from matplotlib.lines import Line2D
            legend_elements.append(
                Line2D([0], [0], 
                      color=colors[i], 
                      linestyle=linestyles[i],
                      marker=markers[i],
                      label=label,
                      linewidth=2,
                      markersize=8)
            )
        
        # 添加图例，优化位置
        legend = ax.legend(
            handles=legend_elements,
            loc=position,
            frameon=True,
            fancybox=True,
            shadow=True,
            framealpha=0.9,
            facecolor='white',
            edgecolor='gray',
            bbox_to_anchor=(1.02, 1) if position == 'outside_right' else None
        )
        
        return legend
    
    def add_value_annotations(self, ax, x_data, y_data, values=None, 
                            offset_y=0.02, fontsize=10):
        """
        添加数值标注
        
        Args:
            ax: 轴对象
            x_data: x坐标数据
            y_data: y坐标数据
            values: 要标注的值（如果为None，使用y_data）
            offset_y: y方向偏移
            fontsize: 字体大小
        """
        if values is None:
            values = y_data
        
        for i, (x, y, val) in enumerate(zip(x_data, y_data, values)):
            ax.annotate(
                f'{val:.3f}' if isinstance(val, float) else str(val),
                xy=(x, y),
                xytext=(0, offset_y),
                textcoords='offset points',
                ha='center',
                va='bottom',
                fontsize=fontsize,
                fontweight='bold',
                bbox=dict(
                    boxstyle='round,pad=0.3',
                    facecolor='white',
                    edgecolor='gray',
                    alpha=0.8
                )
            )
    
    def create_comparison_plot(self, data_before, data_after, 
                             labels, title, ylabel, 
                             before_label='Before', after_label='After'):
        """
        创建对比图表
        
        Args:
            data_before: 处理前数据
            data_after: 处理后数据
            labels: 标签
            title: 标题
            ylabel: y轴标签
            before_label: 处理前标签
            after_label: 处理后标签
        """
        fig = self.create_optimized_figure('single_plot', title)
        ax = fig.add_subplot(111)
        
        x = np.arange(len(labels))
        width = 0.35
        
        # 使用高对比度配色
        colors = self.color_schemes['preprocessing']
        
        bars1 = ax.bar(x - width/2, data_before, width, 
                      label=before_label, 
                      color=colors['raw_data'],
                      alpha=0.8,
                      edgecolor='black',
                      linewidth=1)
        
        bars2 = ax.bar(x + width/2, data_after, width,
                      label=after_label,
                      color=colors['processed_data'],
                      alpha=0.8,
                      edgecolor='black',
                      linewidth=1)
        
        # 设置标签和标题
        ax.set_xlabel('Categories', fontsize=14, fontweight='bold')
        ax.set_ylabel(ylabel, fontsize=14, fontweight='bold')
        ax.set_xticks(x)
        ax.set_xticklabels(labels, rotation=45, ha='right')
        
        # 添加数值标注
        self.add_value_annotations(ax, x - width/2, data_before, data_before)
        self.add_value_annotations(ax, x + width/2, data_after, data_after)
        
        # 创建优化的图例
        ax.legend(loc='upper left', frameon=True, fancybox=True, shadow=True)
        
        # 应用网格
        ax.grid(True, alpha=0.3, axis='y')
        
        # 应用优化布局
        self.apply_optimized_layout(fig, 'single_plot')
        
        return fig, ax
