#!/usr/bin/env python3
"""
GPU可用性和兼容性检查脚本
"""

import sys
import subprocess
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_nvidia_gpu():
    """检查NVIDIA GPU和驱动"""
    print("🔍 检查NVIDIA GPU和驱动...")
    
    try:
        # 检查nvidia-smi
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ NVIDIA GPU驱动已安装")
            print("GPU信息:")
            lines = result.stdout.split('\n')
            for line in lines:
                if 'NVIDIA-SMI' in line or 'Tesla' in line or 'GeForce' in line or 'RTX' in line or 'GTX' in line:
                    print(f"   {line.strip()}")
            return True
        else:
            print("❌ nvidia-smi命令失败")
            return False
    except FileNotFoundError:
        print("❌ 未找到nvidia-smi命令，可能未安装NVIDIA驱动")
        return False
    except subprocess.TimeoutExpired:
        print("❌ nvidia-smi命令超时")
        return False
    except Exception as e:
        print(f"❌ 检查NVIDIA GPU时出错: {str(e)}")
        return False

def check_tensorflow_gpu():
    """检查TensorFlow GPU支持"""
    print("\n🔍 检查TensorFlow GPU支持...")
    
    try:
        import tensorflow as tf
        print(f"✅ TensorFlow版本: {tf.__version__}")
        
        # 检查GPU设备
        gpus = tf.config.list_physical_devices('GPU')
        if gpus:
            print(f"✅ 检测到 {len(gpus)} 个GPU设备:")
            for i, gpu in enumerate(gpus):
                print(f"   GPU {i}: {gpu.name}")
                
            # 检查GPU内存配置
            try:
                for gpu in gpus:
                    tf.config.experimental.set_memory_growth(gpu, True)
                print("✅ GPU内存增长模式已启用")
            except Exception as e:
                print(f"⚠️  GPU内存配置警告: {str(e)}")
                
            # 测试GPU计算
            try:
                with tf.device('/GPU:0'):
                    a = tf.constant([[1.0, 2.0], [3.0, 4.0]])
                    b = tf.constant([[1.0, 1.0], [0.0, 1.0]])
                    c = tf.matmul(a, b)
                print("✅ GPU计算测试成功")
                return True
            except Exception as e:
                print(f"❌ GPU计算测试失败: {str(e)}")
                return False
        else:
            print("❌ 未检测到GPU设备")
            return False
            
    except ImportError:
        print("❌ TensorFlow未安装")
        return False
    except Exception as e:
        print(f"❌ TensorFlow GPU检查失败: {str(e)}")
        return False

def check_pytorch_gpu():
    """检查PyTorch GPU支持"""
    print("\n🔍 检查PyTorch GPU支持...")
    
    try:
        import torch
        print(f"✅ PyTorch版本: {torch.__version__}")
        
        if torch.cuda.is_available():
            print(f"✅ CUDA可用，版本: {torch.version.cuda}")
            print(f"✅ 检测到 {torch.cuda.device_count()} 个GPU设备:")
            
            for i in range(torch.cuda.device_count()):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                print(f"   GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")
            
            # 测试GPU计算
            try:
                device = torch.device('cuda:0')
                x = torch.randn(2, 2).to(device)
                y = torch.randn(2, 2).to(device)
                z = torch.matmul(x, y)
                print("✅ PyTorch GPU计算测试成功")
                return True
            except Exception as e:
                print(f"❌ PyTorch GPU计算测试失败: {str(e)}")
                return False
        else:
            print("❌ PyTorch CUDA不可用")
            return False
            
    except ImportError:
        print("❌ PyTorch未安装")
        return False
    except Exception as e:
        print(f"❌ PyTorch GPU检查失败: {str(e)}")
        return False

def check_xgboost_gpu():
    """检查XGBoost GPU支持"""
    print("\n🔍 检查XGBoost GPU支持...")
    
    try:
        import xgboost as xgb
        print(f"✅ XGBoost版本: {xgb.__version__}")
        
        # 检查GPU支持
        try:
            # 创建一个小的测试数据集
            import numpy as np
            from sklearn.datasets import make_classification
            
            X, y = make_classification(n_samples=100, n_features=10, random_state=42)
            
            # 尝试使用GPU训练
            model = xgb.XGBClassifier(
                tree_method='gpu_hist',
                gpu_id=0,
                n_estimators=10,
                random_state=42
            )
            model.fit(X, y)
            print("✅ XGBoost GPU训练测试成功")
            return True
            
        except Exception as e:
            print(f"❌ XGBoost GPU训练测试失败: {str(e)}")
            print("   可能原因: GPU版本XGBoost未安装或GPU不兼容")
            return False
            
    except ImportError:
        print("❌ XGBoost未安装")
        return False
    except Exception as e:
        print(f"❌ XGBoost GPU检查失败: {str(e)}")
        return False

def check_cuml_availability():
    """检查cuML GPU加速库"""
    print("\n🔍 检查cuML GPU加速库...")
    
    try:
        import cuml
        print(f"✅ cuML版本: {cuml.__version__}")
        
        # 测试cuML Random Forest
        try:
            from cuml.ensemble import RandomForestClassifier as cuRF
            from sklearn.datasets import make_classification
            
            X, y = make_classification(n_samples=100, n_features=10, random_state=42)
            
            model = cuRF(n_estimators=10, random_state=42)
            model.fit(X, y)
            print("✅ cuML GPU Random Forest测试成功")
            return True
            
        except Exception as e:
            print(f"❌ cuML测试失败: {str(e)}")
            return False
            
    except ImportError:
        print("❌ cuML未安装 (这是可选的GPU加速库)")
        return False
    except Exception as e:
        print(f"❌ cuML检查失败: {str(e)}")
        return False

def get_gpu_recommendations():
    """获取GPU优化建议"""
    print("\n💡 GPU优化建议:")
    
    # 检查各个组件
    nvidia_ok = check_nvidia_gpu()
    tf_gpu_ok = check_tensorflow_gpu()
    torch_gpu_ok = check_pytorch_gpu()
    xgb_gpu_ok = check_xgboost_gpu()
    cuml_ok = check_cuml_availability()
    
    print(f"\n📊 GPU支持总结:")
    print(f"   NVIDIA驱动: {'✅' if nvidia_ok else '❌'}")
    print(f"   TensorFlow GPU: {'✅' if tf_gpu_ok else '❌'}")
    print(f"   PyTorch GPU: {'✅' if torch_gpu_ok else '❌'}")
    print(f"   XGBoost GPU: {'✅' if xgb_gpu_ok else '❌'}")
    print(f"   cuML: {'✅' if cuml_ok else '❌'}")
    
    # 提供建议
    print(f"\n🚀 优化建议:")
    
    if not nvidia_ok:
        print("1. 安装NVIDIA GPU驱动程序")
        print("2. 确保GPU硬件兼容CUDA")
    
    if nvidia_ok and not tf_gpu_ok:
        print("1. 重新安装TensorFlow GPU版本: pip install tensorflow[and-cuda]")
        print("2. 检查CUDA版本兼容性")
    
    if nvidia_ok and not xgb_gpu_ok:
        print("1. 安装GPU版本XGBoost: pip install xgboost[gpu]")
        print("2. 或者使用conda: conda install -c conda-forge py-xgboost-gpu")
    
    if nvidia_ok and not cuml_ok:
        print("1. 可选：安装cuML加速scikit-learn: conda install -c rapidsai cuml")
    
    # 返回支持状态
    return {
        'nvidia': nvidia_ok,
        'tensorflow': tf_gpu_ok,
        'pytorch': torch_gpu_ok,
        'xgboost': xgb_gpu_ok,
        'cuml': cuml_ok
    }

def main():
    """主函数"""
    print("🚀 GPU可用性和兼容性检查")
    print("=" * 60)

    gpu_status = get_gpu_recommendations()

    # 生成配置建议
    print(f"\n⚙️  推荐的训练配置:")

    if gpu_status['xgboost']:
        print("   XGBoost: 使用 device='cuda', tree_method='hist'")
    else:
        print("   XGBoost: 使用 tree_method='hist' (CPU)")

    if gpu_status['tensorflow']:
        print("   深度学习: 使用 GPU 加速")
    else:
        print("   深度学习: 使用 CPU 训练")

    if gpu_status['cuml']:
        print("   scikit-learn: 可使用 cuML GPU 加速")
    else:
        print("   scikit-learn: 使用 CPU 训练")

    # 显示当前最佳配置
    print(f"\n🎯 当前最佳GPU配置:")
    print(f"   ✅ PyTorch GPU: 已配置 (CUDA 12.1)")
    print(f"   ✅ XGBoost GPU: 已配置 (device='cuda')")
    print(f"   ❌ TensorFlow GPU: 需要安装CUDA库")

    return gpu_status

if __name__ == "__main__":
    main()
