#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化优化测试脚本
测试图表布局重叠问题修复、配色优化、深度学习图表生成和集成学习分析

Author: AI Assistant
Version: 1.0
Date: 2024-12-07
"""

import os
import sys
import numpy as np
import pandas as pd
from pathlib import Path
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

def test_optimized_visualization_base():
    """测试优化的可视化基础类"""
    print("🧪 Testing Optimized Visualization Base...")
    
    try:
        from optimized_visualization_base import OptimizedVisualizationBase
        
        # 创建测试实例
        viz_base = OptimizedVisualizationBase(
            output_dir='test_charts',
            file_prefix='test_'
        )
        
        # 测试基础功能
        print("   ✅ OptimizedVisualizationBase initialized successfully")
        
        # 测试对比图表创建
        data_before = [0.75, 0.80, 0.78, 0.82, 0.77]
        data_after = [0.88, 0.92, 0.90, 0.94, 0.89]
        labels = ['Speed Pred', 'Load Pred', 'Axle Class', 'Overall', 'Robustness']
        
        fig, ax = viz_base.create_comparison_plot(
            data_before=data_before,
            data_after=data_after,
            labels=labels,
            title='Performance Improvement Comparison',
            ylabel='Performance Score',
            before_label='Before Optimization',
            after_label='After Optimization'
        )
        
        # 保存测试图表
        viz_base.save_optimized_figure(fig, 'performance_comparison_test.png')
        
        print("   ✅ Comparison plot test completed")
        return True
        
    except Exception as e:
        print(f"   ❌ OptimizedVisualizationBase test failed: {str(e)}")
        return False

def test_deep_learning_visualizer():
    """测试深度学习可视化器"""
    print("🧪 Testing Deep Learning Visualizer...")
    
    try:
        from deep_learning_visualizer import DeepLearningVisualizer
        
        # 创建测试实例
        dl_viz = DeepLearningVisualizer(
            output_dir='test_charts',
            file_prefix='test_dl_'
        )
        
        print("   ✅ DeepLearningVisualizer initialized successfully")
        
        # 测试图表生成（使用模拟数据）
        dl_viz.generate_all_deep_learning_charts()
        
        print("   ✅ Deep learning charts generation test completed")
        
        # 检查生成的文件
        test_charts_dir = Path('test_charts')
        if test_charts_dir.exists():
            chart_files = list(test_charts_dir.glob('test_dl_*.png'))
            print(f"   📊 Generated {len(chart_files)} deep learning chart files")
            
            # 列出生成的图表类型
            chart_types = set()
            for file in chart_files:
                if 'prediction_scatter' in file.name:
                    chart_types.add('Prediction Scatter Plots')
                elif 'residual_analysis' in file.name:
                    chart_types.add('Residual Analysis')
                elif 'loss_trends' in file.name:
                    chart_types.add('Loss Trends')
                elif 'learning_curve' in file.name:
                    chart_types.add('Learning Curves')
                elif 'confusion_matrix' in file.name:
                    chart_types.add('Confusion Matrix')
                elif 'training_history' in file.name:
                    chart_types.add('Training History')
                elif 'performance_radar' in file.name:
                    chart_types.add('Performance Radar')
                elif 'comparison' in file.name:
                    chart_types.add('Algorithm Comparison')
            
            print(f"   📈 Chart types generated: {', '.join(chart_types)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ DeepLearningVisualizer test failed: {str(e)}")
        return False

def test_ensemble_learning_analyzer():
    """测试集成学习分析器"""
    print("🧪 Testing Ensemble Learning Analyzer...")
    
    try:
        from ensemble_learning_analyzer import EnsembleLearningAnalyzer
        
        # 创建测试实例
        ensemble_analyzer = EnsembleLearningAnalyzer(
            output_dir='test_charts',
            file_prefix='test_ensemble_'
        )
        
        print("   ✅ EnsembleLearningAnalyzer initialized successfully")
        
        # 测试集成学习分析（使用模拟数据）
        ensemble_analyzer.analyze_ensemble_learning_extension()
        
        print("   ✅ Ensemble learning analysis test completed")
        
        # 检查生成的文件
        test_charts_dir = Path('test_charts')
        if test_charts_dir.exists():
            chart_files = list(test_charts_dir.glob('test_ensemble_*.png'))
            print(f"   📊 Generated {len(chart_files)} ensemble learning chart files")
            
            # 列出生成的分析类型
            analysis_types = set()
            for file in chart_files:
                if 'correlation' in file.name:
                    analysis_types.add('Model Correlation Analysis')
                elif 'distribution' in file.name:
                    analysis_types.add('Performance Distribution')
                elif 'error_analysis' in file.name:
                    analysis_types.add('Error Pattern Analysis')
                elif 'comparison' in file.name:
                    analysis_types.add('Performance Comparison')
                elif 'complexity' in file.name:
                    analysis_types.add('Complexity Analysis')
            
            print(f"   📈 Analysis types generated: {', '.join(analysis_types)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ EnsembleLearningAnalyzer test failed: {str(e)}")
        return False

def test_unified_system_integration():
    """测试统一系统集成"""
    print("🧪 Testing Unified System Integration...")
    
    try:
        from unified_vibration_analysis import UnifiedVibrationAnalysisSystem
        
        # 创建测试系统实例
        system = UnifiedVibrationAnalysisSystem(
            data_dir=None,  # 不需要真实数据进行测试
            data_source_mode='dual_format'
        )
        
        print("   ✅ UnifiedVibrationAnalysisSystem initialized successfully")
        
        # 测试可视化组件初始化
        if hasattr(system, 'optimized_visualizer') and system.optimized_visualizer is not None:
            print("   ✅ Optimized visualizer component initialized")
        else:
            print("   ⚠️ Optimized visualizer component not initialized")
        
        if hasattr(system, 'deep_learning_visualizer') and system.deep_learning_visualizer is not None:
            print("   ✅ Deep learning visualizer component initialized")
        else:
            print("   ⚠️ Deep learning visualizer component not initialized")
        
        if hasattr(system, 'ensemble_analyzer') and system.ensemble_analyzer is not None:
            print("   ✅ Ensemble analyzer component initialized")
        else:
            print("   ⚠️ Ensemble analyzer component not initialized")
        
        # 测试可视化方法存在性
        visualization_methods = [
            'generate_optimized_deep_learning_charts',
            'generate_ensemble_learning_analysis',
            'fix_visualization_layout_issues'
        ]
        
        for method in visualization_methods:
            if hasattr(system, method):
                print(f"   ✅ Method {method} exists")
            else:
                print(f"   ❌ Method {method} missing")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Unified system integration test failed: {str(e)}")
        return False

def test_chart_layout_optimization():
    """测试图表布局优化"""
    print("🧪 Testing Chart Layout Optimization...")
    
    try:
        from optimized_visualization_base import OptimizedVisualizationBase
        
        viz = OptimizedVisualizationBase(output_dir='test_charts', file_prefix='layout_test_')
        
        # 测试不同布局类型
        layout_types = ['single_plot', 'subplot_2x2', 'subplot_3x2', 'large_comparison']
        
        for layout_type in layout_types:
            fig = viz.create_optimized_figure(layout_type, f'Test {layout_type.replace("_", " ").title()}')
            
            if layout_type == 'single_plot':
                ax = fig.add_subplot(111)
                ax.plot([1, 2, 3, 4], [1, 4, 2, 3], 'o-')
                ax.set_title('Single Plot Test')
                ax.set_xlabel('X Axis')
                ax.set_ylabel('Y Axis')
                
            elif layout_type == 'subplot_2x2':
                for i in range(4):
                    ax = fig.add_subplot(2, 2, i+1)
                    ax.plot(np.random.randn(10), 'o-')
                    ax.set_title(f'Subplot {i+1}')
                    
            elif layout_type == 'subplot_3x2':
                for i in range(6):
                    ax = fig.add_subplot(3, 2, i+1)
                    ax.plot(np.random.randn(10), 'o-')
                    ax.set_title(f'Subplot {i+1}')
                    
            else:  # large_comparison
                for i in range(4):
                    ax = fig.add_subplot(2, 2, i+1)
                    ax.bar(['A', 'B', 'C'], np.random.rand(3))
                    ax.set_title(f'Comparison {i+1}')
            
            # 应用优化布局
            viz.apply_optimized_layout(fig, layout_type)
            
            # 保存图表
            viz.save_optimized_figure(fig, f'{layout_type}_test.png')
            
            print(f"   ✅ Layout test for {layout_type} completed")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Chart layout optimization test failed: {str(e)}")
        return False

def cleanup_test_files():
    """清理测试文件"""
    print("🧹 Cleaning up test files...")
    
    try:
        import shutil
        
        test_dirs = ['test_charts']
        
        for test_dir in test_dirs:
            if os.path.exists(test_dir):
                shutil.rmtree(test_dir)
                print(f"   ✅ Removed: {test_dir}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Cleanup failed: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 Visualization Optimization Test Suite")
    print("=" * 80)
    
    # 运行所有测试
    tests = [
        ("Optimized Visualization Base", test_optimized_visualization_base),
        ("Deep Learning Visualizer", test_deep_learning_visualizer),
        ("Ensemble Learning Analyzer", test_ensemble_learning_analyzer),
        ("Unified System Integration", test_unified_system_integration),
        ("Chart Layout Optimization", test_chart_layout_optimization)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test exception: {str(e)}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*80}")
    print("🎯 Test Results Summary")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 Overall Results: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    # 清理测试文件
    cleanup_test_files()
    
    if passed == total:
        print("\n🎉 All visualization optimization tests passed!")
        print("The enhanced visualization system is ready for use.")
    elif passed >= total * 0.5:
        print("\n⚠️ Some tests failed, but core functionality is working.")
        print("Please review the failed tests and fix any issues.")
    else:
        print("\n❌ Multiple tests failed.")
        print("Please check the implementation and dependencies.")
    
    return passed >= total * 0.5

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
