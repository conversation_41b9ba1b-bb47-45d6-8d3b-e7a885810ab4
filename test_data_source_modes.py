#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Script for Data Source Mode Control in Unified Vibration Analysis System
Tests the flexible data source processing modes

Author: AI Assistant
Version: 1.0
Date: 2024-12-07
"""

import os
import sys
import numpy as np
import pandas as pd
from pathlib import Path
import shutil

def create_test_legacy_data():
    """Create test legacy format data (21 columns)"""
    print("🔧 Creating test legacy format data...")
    
    try:
        # Create directory structure for legacy data
        legacy_dir = Path('test_legacy_data')
        legacy_dir.mkdir(exist_ok=True)
        
        # Create nested directory structure: weight/axle/speed
        weights = ['2.0吨', '3.0吨', '25.0吨']
        axle_types = ['双轴', '三轴']
        speeds = ['40.0kmh', '60.0kmh', '80.0kmh']
        
        file_count = 0
        
        for weight in weights:
            weight_dir = legacy_dir / weight
            weight_dir.mkdir(exist_ok=True)
            
            for axle_type in axle_types:
                axle_dir = weight_dir / axle_type
                axle_dir.mkdir(exist_ok=True)
                
                for speed in speeds:
                    speed_dir = axle_dir / speed
                    speed_dir.mkdir(exist_ok=True)
                    
                    # Create 3 CSV files per speed directory
                    for i in range(3):
                        # Generate synthetic data (21 columns: count + 20 sensors)
                        n_samples = 1000
                        data = {
                            'count': range(n_samples)
                        }
                        
                        # Add 20 sensor columns
                        for sensor_id in range(1, 21):
                            # Generate realistic vibration signal
                            signal = np.random.normal(0, 0.5, n_samples)
                            
                            # Add vehicle passage event
                            event_center = 500  # Middle of the signal
                            event_width = 200
                            
                            for j in range(event_center - event_width//2, 
                                         event_center + event_width//2):
                                if 0 <= j < n_samples:
                                    # Add damped oscillation
                                    t = (j - event_center) / 100
                                    amplitude = float(weight.replace('吨', '')) * 0.5
                                    frequency = 50 + sensor_id
                                    signal[j] += amplitude * np.exp(-abs(t)) * np.sin(2*np.pi*frequency*t/10)
                            
                            data[f'Sensor_{sensor_id:02d}'] = signal
                        
                        # Save CSV file
                        df = pd.DataFrame(data)
                        filename = f'{weight}_{axle_type}_{speed}_实验{i+1}.csv'
                        file_path = speed_dir / filename
                        df.to_csv(file_path, index=False)
                        file_count += 1
        
        print(f"   ✅ Created {file_count} legacy format files")
        print(f"   📂 Directory: {legacy_dir}")
        return str(legacy_dir)
        
    except Exception as e:
        print(f"   ❌ Error creating legacy data: {str(e)}")
        return None

def create_test_new_data():
    """Create test new format data (22 columns)"""
    print("🔧 Creating test new format data...")
    
    try:
        # Create directory for new format data
        new_dir = Path('test_new_data')
        new_dir.mkdir(exist_ok=True)
        
        file_count = 0
        
        # Create new format files with naming pattern: GW100001_datetime_AcceData_lane_axles-weight-speed.csv
        test_configs = [
            ('车道1', '2轴', '3t', '72kmh'),
            ('车道1', '2轴', '1.4t', '100kmh'),
            ('车道1', '3轴', '25t', '60kmh'),
            ('车道2', '2轴', '2t', '80kmh'),
            ('车道2', '3轴', '15t', '45kmh')
        ]
        
        for i, (lane, axles, weight, speed) in enumerate(test_configs):
            # Generate multiple files for each configuration
            for j in range(3):
                # Generate synthetic data (22 columns: count + 20 sensors + timestamp)
                n_samples = 1000
                
                data = {
                    'count': range(n_samples)
                }
                
                # Add 20 sensor columns
                for sensor_id in range(1, 21):
                    # Generate realistic vibration signal
                    signal = np.random.normal(0, 0.3, n_samples)
                    
                    # Add vehicle passage event
                    event_center = 500  # Middle of the signal
                    event_width = 200
                    
                    for k in range(event_center - event_width//2, 
                                 event_center + event_width//2):
                        if 0 <= k < n_samples:
                            # Add damped oscillation
                            t = (k - event_center) / 100
                            amplitude = float(weight.replace('t', '')) * 0.8
                            frequency = 45 + sensor_id * 2
                            signal[k] += amplitude * np.exp(-abs(t)*2) * np.sin(2*np.pi*frequency*t/10)
                    
                    data[f'Sensor_{sensor_id:02d}'] = signal
                
                # Add timestamp column
                timestamps = pd.date_range('2024-01-01 10:00:00', periods=n_samples, freq='1ms')
                data['timestamp'] = timestamps
                
                # Create filename in new format
                datetime_str = f'2024010{i+1}10{j:02d}{i:02d}'
                filename = f'GW100001_{datetime_str}_AcceData_{lane}_{axles}-{weight}-{speed}.csv'
                
                # Save CSV file
                df = pd.DataFrame(data)
                file_path = new_dir / filename
                df.to_csv(file_path, index=False)
                file_count += 1
        
        print(f"   ✅ Created {file_count} new format files")
        print(f"   📂 Directory: {new_dir}")
        return str(new_dir)
        
    except Exception as e:
        print(f"   ❌ Error creating new data: {str(e)}")
        return None

def test_data_source_mode(mode, data_dir=None):
    """Test specific data source mode"""
    print(f"\n🧪 Testing data source mode: {mode}")
    print("=" * 60)
    
    try:
        from unified_vibration_analysis import UnifiedVibrationAnalysisSystem
        
        # Create system with specified mode
        system = UnifiedVibrationAnalysisSystem(
            data_dir=data_dir,
            data_source_mode=mode
        )
        
        # Disable some features for faster testing
        system.optimization_enabled = False
        system.process_visualization_enabled = False
        system.sensor_optimization_enabled = False
        system.denoising_enabled = False
        
        print(f"   📊 Mode: {mode}")
        print(f"   📂 Data directory: {data_dir}")
        
        # Test feature extraction
        print(f"   🔄 Testing feature extraction...")
        features_df = system.extract_features_from_data()
        
        if features_df is not None and not features_df.empty:
            print(f"   ✅ Feature extraction successful")
            print(f"   📈 Features shape: {features_df.shape}")
            print(f"   📋 Columns: {list(features_df.columns)[:10]}...")
            
            # Check data source identification
            if 'data_source' in features_df.columns:
                source_counts = features_df['data_source'].value_counts()
                print(f"   📊 Data source distribution:")
                for source, count in source_counts.items():
                    print(f"      {source}: {count} records")
            
            if 'processing_mode' in features_df.columns:
                mode_counts = features_df['processing_mode'].value_counts()
                print(f"   🔧 Processing mode: {list(mode_counts.index)}")
            
            return True
        else:
            print(f"   ❌ Feature extraction failed")
            return False
            
    except Exception as e:
        print(f"   ❌ Test failed: {str(e)}")
        return False

def test_command_line_interface():
    """Test command line interface"""
    print(f"\n🧪 Testing command line interface...")
    print("=" * 60)
    
    try:
        # Test help message
        print("   📋 Testing help message...")
        os.system("python unified_vibration_analysis.py --help")
        
        # Test different modes (dry run)
        modes = ['legacy_only', 'new_only', 'dual_format']
        
        for mode in modes:
            print(f"   🔧 Testing mode: {mode}")
            # Note: This would be a dry run test in a real scenario
            print(f"      Command: python unified_vibration_analysis.py --mode {mode}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Command line test failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 Testing Data Source Mode Control")
    print("=" * 80)
    
    # Create test data
    legacy_dir = create_test_legacy_data()
    new_dir = create_test_new_data()
    
    if not legacy_dir or not new_dir:
        print("❌ Failed to create test data")
        return False
    
    # Test results
    results = {}
    
    # Test legacy_only mode
    print(f"\n{'='*60}")
    results['legacy_only'] = test_data_source_mode('legacy_only', legacy_dir)
    
    # Test new_only mode
    print(f"\n{'='*60}")
    results['new_only'] = test_data_source_mode('new_only', new_dir)
    
    # Test dual_format mode (create combined directory)
    print(f"\n{'='*60}")
    combined_dir = Path('test_combined_data')
    combined_dir.mkdir(exist_ok=True)
    
    # Copy both types of data to combined directory
    if legacy_dir and os.path.exists(legacy_dir):
        shutil.copytree(legacy_dir, combined_dir / 'legacy', dirs_exist_ok=True)
    if new_dir and os.path.exists(new_dir):
        shutil.copytree(new_dir, combined_dir / 'new', dirs_exist_ok=True)
    
    results['dual_format'] = test_data_source_mode('dual_format', str(combined_dir))
    
    # Test command line interface
    results['cli'] = test_command_line_interface()
    
    # Summary
    print(f"\n{'='*80}")
    print("🎯 Test Results Summary")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 Overall Results: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    # Cleanup
    cleanup_test_data()
    
    if passed == total:
        print("\n🎉 All data source mode tests passed!")
        print("The unified_vibration_analysis.py system supports flexible data source control.")
        print("\n📋 Usage Examples:")
        print("   python unified_vibration_analysis.py                    # Default dual format")
        print("   python unified_vibration_analysis.py --mode legacy_only # Legacy format only")
        print("   python unified_vibration_analysis.py --mode new_only    # New format only")
    else:
        print("\n❌ Some tests failed.")
        print("Please check the implementation and try again.")
    
    return passed == total

def cleanup_test_data():
    """Clean up test data directories"""
    print(f"\n🧹 Cleaning up test data...")
    
    test_dirs = [
        'test_legacy_data',
        'test_new_data', 
        'test_combined_data'
    ]
    
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
            print(f"   ✅ Removed: {test_dir}")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
