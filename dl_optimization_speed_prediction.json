{"optimization_settings": {"n_trials": 5, "random_state": 42}, "best_parameters": {"BP Neural Network": {"hidden_layers": 4, "neurons_layer1": 102, "neurons_layer2": 74, "neurons_layer3": 39, "dropout_rate": 0.17394178221021084, "learning_rate": 0.00869299151113955, "batch_size": 32, "activation": "relu", "batch_norm": false}, "CNN-LSTM": {"timesteps": 44, "cnn_filters1": 52, "cnn_filters2": 24, "cnn_kernel_size": 3, "lstm_units1": 61, "lstm_units2": 41, "dense_units": 37, "dropout_rate": 0.21649165607921678, "learning_rate": 0.0016738085788752138, "batch_size": 64}, "TCN": {"timesteps": 36, "nb_filters": 52, "kernel_size": 3, "nb_stacks": 1, "dilations": [1, 2, 4, 8], "dropout_rate": 0.1873687420594126, "learning_rate": 0.0016738085788752138, "batch_size": 64}}, "optimization_results": {"BP Neural Network": {"best_score": 0.009419631889284852, "best_params": {"hidden_layers": 4, "neurons_layer1": 102, "neurons_layer2": 74, "neurons_layer3": 39, "dropout_rate": 0.17394178221021084, "learning_rate": 0.00869299151113955, "batch_size": 32, "activation": "relu", "batch_norm": false}, "n_trials": 5}, "CNN-LSTM": {"best_score": -1.674872608501997, "best_params": {"timesteps": 44, "cnn_filters1": 52, "cnn_filters2": 24, "cnn_kernel_size": 3, "lstm_units1": 61, "lstm_units2": 41, "dense_units": 37, "dropout_rate": 0.21649165607921678, "learning_rate": 0.0016738085788752138, "batch_size": 64}, "n_trials": 5}, "TCN": {"best_score": -2.2857213620632475, "best_params": {"timesteps": 36, "nb_filters": 52, "kernel_size": 3, "nb_stacks": 1, "dilations": [1, 2, 4, 8], "dropout_rate": 0.1873687420594126, "learning_rate": 0.0016738085788752138, "batch_size": 64}, "n_trials": 5}}}