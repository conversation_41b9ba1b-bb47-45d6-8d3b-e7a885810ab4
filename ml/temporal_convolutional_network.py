#!/usr/bin/env python3
"""
时间卷积网络（TCN - Temporal Convolutional Network）实现
专门用于处理振动信号的时间序列特征
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import KFold, StratifiedKFold
from sklearn.metrics import r2_score, accuracy_score
import optuna
import logging
import time
from typing import Dict, Any, Tuple, Optional, List
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TemporalBlock(nn.Module):
    """TCN的基本时间块"""
    
    def __init__(self, n_inputs: int, n_outputs: int, kernel_size: int, stride: int, 
                 dilation: int, padding: int, dropout: float = 0.2):
        """
        初始化时间块
        
        Args:
            n_inputs: 输入通道数
            n_outputs: 输出通道数
            kernel_size: 卷积核大小
            stride: 步长
            dilation: 扩张率
            padding: 填充
            dropout: Dropout比率
        """
        super(TemporalBlock, self).__init__()
        
        # 第一个卷积层
        self.conv1 = nn.utils.weight_norm(nn.Conv1d(
            n_inputs, n_outputs, kernel_size,
            stride=stride, padding=padding, dilation=dilation
        ))
        
        # 第二个卷积层
        self.conv2 = nn.utils.weight_norm(nn.Conv1d(
            n_outputs, n_outputs, kernel_size,
            stride=stride, padding=padding, dilation=dilation
        ))
        
        # Dropout层
        self.dropout1 = nn.Dropout(dropout)
        self.dropout2 = nn.Dropout(dropout)
        
        # 层归一化
        self.norm1 = nn.LayerNorm(n_outputs)
        self.norm2 = nn.LayerNorm(n_outputs)
        
        # 残差连接的投影层
        self.downsample = nn.Conv1d(n_inputs, n_outputs, 1) if n_inputs != n_outputs else None
        
        # 激活函数
        self.relu = nn.ReLU()
        
        self.init_weights()
    
    def init_weights(self):
        """初始化权重"""
        self.conv1.weight.data.normal_(0, 0.01)
        self.conv2.weight.data.normal_(0, 0.01)
        if self.downsample is not None:
            self.downsample.weight.data.normal_(0, 0.01)
    
    def forward(self, x):
        """前向传播"""
        # 第一个卷积块
        out = self.conv1(x)
        # 因果裁剪：移除右侧的填充
        if out.size(2) > x.size(2):
            out = out[:, :, :x.size(2)]
        out = self.relu(out)
        out = self.dropout1(out)

        # 层归一化（需要转置以适应LayerNorm）
        out = out.transpose(1, 2)
        out = self.norm1(out)
        out = out.transpose(1, 2)

        # 第二个卷积块
        out = self.conv2(out)
        # 因果裁剪
        if out.size(2) > x.size(2):
            out = out[:, :, :x.size(2)]
        out = self.relu(out)
        out = self.dropout2(out)

        # 层归一化
        out = out.transpose(1, 2)
        out = self.norm2(out)
        out = out.transpose(1, 2)

        # 残差连接
        res = x if self.downsample is None else self.downsample(x)

        return self.relu(out + res)

class TemporalConvNet(nn.Module):
    """时间卷积网络"""
    
    def __init__(self, num_inputs: int, num_channels: List[int], kernel_size: int = 2, 
                 dropout: float = 0.2):
        """
        初始化TCN
        
        Args:
            num_inputs: 输入特征数
            num_channels: 每层的通道数列表
            kernel_size: 卷积核大小
            dropout: Dropout比率
        """
        super(TemporalConvNet, self).__init__()
        
        layers = []
        num_levels = len(num_channels)
        
        for i in range(num_levels):
            dilation_size = 2 ** i
            in_channels = num_inputs if i == 0 else num_channels[i-1]
            out_channels = num_channels[i]

            # 使用因果填充，确保输出长度与输入相同
            padding = (kernel_size - 1) * dilation_size

            layers += [TemporalBlock(
                in_channels, out_channels, kernel_size, stride=1,
                dilation=dilation_size,
                padding=padding,
                dropout=dropout
            )]
        
        self.network = nn.Sequential(*layers)
    
    def forward(self, x):
        """前向传播"""
        return self.network(x)

class TCNModel(nn.Module):
    """完整的TCN模型"""
    
    def __init__(self, input_size: int, num_channels: List[int], num_classes: int,
                 kernel_size: int = 2, dropout: float = 0.2, sequence_length: int = 100):
        """
        初始化TCN模型
        
        Args:
            input_size: 输入特征维度
            num_channels: TCN层的通道数列表
            num_classes: 输出类别数（回归为1）
            kernel_size: 卷积核大小
            dropout: Dropout比率
            sequence_length: 序列长度
        """
        super(TCNModel, self).__init__()
        
        self.input_size = input_size
        self.sequence_length = sequence_length
        
        # 输入投影层（将特征转换为序列）
        self.input_projection = nn.Linear(input_size, sequence_length)
        
        # TCN网络
        self.tcn = TemporalConvNet(1, num_channels, kernel_size, dropout)
        
        # 全局平均池化
        self.global_avg_pool = nn.AdaptiveAvgPool1d(1)
        
        # 分类/回归头
        self.classifier = nn.Sequential(
            nn.Linear(num_channels[-1], num_channels[-1] // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(num_channels[-1] // 2, num_classes)
        )
    
    def forward(self, x):
        """前向传播"""
        batch_size = x.size(0)
        
        # 将输入特征投影到序列
        x = self.input_projection(x)  # (batch_size, sequence_length)
        x = x.unsqueeze(1)  # (batch_size, 1, sequence_length)
        
        # TCN处理
        x = self.tcn(x)  # (batch_size, num_channels[-1], sequence_length)
        
        # 全局平均池化
        x = self.global_avg_pool(x)  # (batch_size, num_channels[-1], 1)
        x = x.squeeze(-1)  # (batch_size, num_channels[-1])
        
        # 分类/回归
        x = self.classifier(x)
        
        return x

class TCNTrainer:
    """TCN训练器"""
    
    def __init__(self, task_type: str = 'regression', device: str = None):
        """
        初始化训练器
        
        Args:
            task_type: 任务类型 ('regression' 或 'classification')
            device: 计算设备
        """
        self.task_type = task_type
        self.device = device if device else ('cuda' if torch.cuda.is_available() else 'cpu')
        self.scaler = StandardScaler()
        
        logger.info(f"TCN训练器初始化完成")
        logger.info(f"任务类型: {task_type}")
        logger.info(f"计算设备: {self.device}")
        
        if self.device == 'cuda':
            logger.info(f"GPU设备: {torch.cuda.get_device_name(0)}")
    
    def prepare_data(self, X: np.ndarray, y: np.ndarray, batch_size: int = 32) -> Tuple[DataLoader, torch.Tensor, torch.Tensor]:
        """准备训练数据"""
        # 标准化特征
        X_scaled = self.scaler.fit_transform(X)
        
        # 转换为PyTorch张量
        X_tensor = torch.FloatTensor(X_scaled).to(self.device)
        
        if self.task_type == 'regression':
            y_tensor = torch.FloatTensor(y.reshape(-1, 1)).to(self.device)
        else:
            # 分类任务
            unique_labels = np.unique(y)
            label_map = {label: idx for idx, label in enumerate(unique_labels)}
            y_mapped = np.array([label_map[label] for label in y])
            y_tensor = torch.LongTensor(y_mapped).to(self.device)
        
        # 创建数据集和数据加载器
        dataset = TensorDataset(X_tensor, y_tensor)
        dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)
        
        return dataloader, X_tensor, y_tensor
    
    def create_model(self, input_size: int, num_channels: List[int], 
                    kernel_size: int = 2, dropout: float = 0.2, 
                    sequence_length: int = 100) -> TCNModel:
        """创建TCN模型"""
        if self.task_type == 'regression':
            num_classes = 1
        else:
            num_classes = len(np.unique(self.current_y)) if hasattr(self, 'current_y') else 2
        
        model = TCNModel(
            input_size=input_size,
            num_channels=num_channels,
            num_classes=num_classes,
            kernel_size=kernel_size,
            dropout=dropout,
            sequence_length=sequence_length
        ).to(self.device)
        
        return model
    
    def train_model(self, model: TCNModel, dataloader: DataLoader,
                   epochs: int = 100, learning_rate: float = 0.001,
                   weight_decay: float = 1e-4, patience: int = 10) -> Dict[str, List[float]]:
        """训练模型"""
        # 选择损失函数
        if self.task_type == 'regression':
            criterion = nn.MSELoss()
        else:
            criterion = nn.CrossEntropyLoss()
        
        # 优化器
        optimizer = optim.Adam(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
        
        # 学习率调度器
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=5, factor=0.5)
        
        # 训练历史
        history = {'loss': [], 'lr': []}
        best_loss = float('inf')
        patience_counter = 0
        
        model.train()
        for epoch in range(epochs):
            epoch_loss = 0.0
            num_batches = 0
            
            for batch_X, batch_y in dataloader:
                optimizer.zero_grad()
                
                # 前向传播
                outputs = model(batch_X)
                
                if self.task_type == 'regression':
                    loss = criterion(outputs, batch_y)
                else:
                    loss = criterion(outputs, batch_y)
                
                # 反向传播
                loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                
                optimizer.step()
                
                epoch_loss += loss.item()
                num_batches += 1
            
            avg_loss = epoch_loss / num_batches
            history['loss'].append(avg_loss)
            history['lr'].append(optimizer.param_groups[0]['lr'])
            
            # 学习率调度
            scheduler.step(avg_loss)
            
            # 早停检查
            if avg_loss < best_loss:
                best_loss = avg_loss
                patience_counter = 0
            else:
                patience_counter += 1
                if patience_counter >= patience:
                    logger.info(f"早停触发，在第 {epoch+1} 轮停止训练")
                    break
            
            if (epoch + 1) % 20 == 0:
                logger.info(f"Epoch {epoch+1}/{epochs}, Loss: {avg_loss:.6f}")
        
        return history
    
    def evaluate_model(self, model: TCNModel, X: torch.Tensor, y: torch.Tensor) -> float:
        """评估模型性能"""
        model.eval()
        with torch.no_grad():
            outputs = model(X)
            
            if self.task_type == 'regression':
                y_pred = outputs.cpu().numpy().flatten()
                y_true = y.cpu().numpy().flatten()
                score = r2_score(y_true, y_pred)
            else:
                y_pred = torch.argmax(outputs, dim=1).cpu().numpy()
                y_true = y.cpu().numpy()
                score = accuracy_score(y_true, y_pred)
        
        return score
    
    def cross_validate(self, X: np.ndarray, y: np.ndarray, num_channels: List[int],
                      kernel_size: int = 2, dropout: float = 0.2,
                      sequence_length: int = 100, epochs: int = 100,
                      learning_rate: float = 0.001, batch_size: int = 32,
                      cv_folds: int = 5) -> float:
        """交叉验证评估"""
        self.current_y = y
        
        if self.task_type == 'regression':
            kf = KFold(n_splits=cv_folds, shuffle=True, random_state=42)
        else:
            kf = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)
        
        scores = []
        
        for fold, (train_idx, val_idx) in enumerate(kf.split(X, y)):
            X_train, X_val = X[train_idx], X[val_idx]
            y_train, y_val = y[train_idx], y[val_idx]
            
            # 准备训练数据
            train_loader, _, _ = self.prepare_data(X_train, y_train, batch_size)
            
            # 准备验证数据
            val_scaler = StandardScaler()
            X_val_scaled = val_scaler.fit_transform(X_val)
            X_val_tensor = torch.FloatTensor(X_val_scaled).to(self.device)
            
            if self.task_type == 'regression':
                y_val_tensor = torch.FloatTensor(y_val.reshape(-1, 1)).to(self.device)
            else:
                unique_labels = np.unique(y)
                label_map = {label: idx for idx, label in enumerate(unique_labels)}
                y_val_mapped = np.array([label_map[label] for label in y_val])
                y_val_tensor = torch.LongTensor(y_val_mapped).to(self.device)
            
            # 创建和训练模型
            model = self.create_model(X.shape[1], num_channels, kernel_size, dropout, sequence_length)
            self.train_model(model, train_loader, epochs, learning_rate)
            
            # 评估
            score = self.evaluate_model(model, X_val_tensor, y_val_tensor)
            scores.append(score)
            
            logger.info(f"Fold {fold+1}/{cv_folds}: {score:.4f}")
        
        mean_score = np.mean(scores)
        logger.info(f"TCN交叉验证平均分数: {mean_score:.4f} ± {np.std(scores):.4f}")
        
        return mean_score

class TCNOptimizer:
    """TCN贝叶斯优化器"""
    
    def __init__(self, task_type: str = 'regression', n_trials: int = 30, cv_folds: int = 5):
        """
        初始化优化器
        
        Args:
            task_type: 任务类型
            n_trials: 优化试验次数
            cv_folds: 交叉验证折数
        """
        self.task_type = task_type
        self.n_trials = n_trials
        self.cv_folds = cv_folds
        self.trainer = TCNTrainer(task_type)
        self.best_params = None
        self.best_score = None
    
    def optimize(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """执行贝叶斯优化"""
        logger.info(f"开始TCN贝叶斯优化...")
        logger.info(f"数据形状: {X.shape}, 任务类型: {self.task_type}")
        
        def objective(trial):
            # TCN结构参数
            n_levels = trial.suggest_int('n_levels', 3, 6)
            base_channels = trial.suggest_int('base_channels', 32, 128)
            num_channels = [base_channels * (2 ** i) for i in range(n_levels)]
            
            # 其他参数
            kernel_size = trial.suggest_int('kernel_size', 2, 5)
            dropout = trial.suggest_float('dropout', 0.1, 0.5)
            sequence_length = trial.suggest_int('sequence_length', 50, 200)
            learning_rate = trial.suggest_float('learning_rate', 1e-4, 1e-2, log=True)
            batch_size = trial.suggest_categorical('batch_size', [16, 32, 64])
            epochs = trial.suggest_int('epochs', 50, 150)
            
            try:
                score = self.trainer.cross_validate(
                    X, y, num_channels, kernel_size, dropout,
                    sequence_length, epochs, learning_rate, batch_size, self.cv_folds
                )
                return score
            except Exception as e:
                logger.warning(f"TCN试验失败: {str(e)}")
                return 0.0
        
        # 创建研究对象
        study = optuna.create_study(
            direction='maximize',
            sampler=optuna.samplers.TPESampler(seed=42)
        )
        
        # 执行优化
        study.optimize(objective, n_trials=self.n_trials)
        
        self.best_params = study.best_params
        self.best_score = study.best_value
        
        logger.info(f"TCN最佳参数: {self.best_params}")
        logger.info(f"TCN最佳分数: {self.best_score:.4f}")
        
        return {
            'best_params': self.best_params,
            'best_score': self.best_score,
            'study': study
        }

def create_optimized_tcn_model(X: np.ndarray, y: np.ndarray, task_type: str = 'regression',
                              n_trials: int = 30) -> Tuple[TCNModel, Dict[str, Any]]:
    """创建优化的TCN模型"""
    optimizer = TCNOptimizer(task_type, n_trials)
    optimization_results = optimizer.optimize(X, y)
    
    # 使用最佳参数创建模型
    trainer = TCNTrainer(task_type)
    trainer.current_y = y
    
    best_params = optimization_results['best_params']
    
    # 构建通道列表
    n_levels = best_params['n_levels']
    base_channels = best_params['base_channels']
    num_channels = [base_channels * (2 ** i) for i in range(n_levels)]
    
    model = trainer.create_model(
        input_size=X.shape[1],
        num_channels=num_channels,
        kernel_size=best_params['kernel_size'],
        dropout=best_params['dropout'],
        sequence_length=best_params['sequence_length']
    )
    
    return model, optimization_results

if __name__ == "__main__":
    # 测试TCN
    from sklearn.datasets import make_regression, make_classification
    
    print("🧪 测试时间卷积网络...")
    
    # 测试回归
    print("\n📊 回归任务测试:")
    X_reg, y_reg = make_regression(n_samples=500, n_features=20, noise=0.1, random_state=42)
    
    trainer_reg = TCNTrainer('regression')
    score_reg = trainer_reg.cross_validate(X_reg, y_reg, [64, 128, 64], epochs=30)
    print(f"回归任务交叉验证分数: {score_reg:.4f}")
    
    print("✅ TCN测试完成")
