#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主程序中的legacy_only模式
重现用户遇到的问题

作者: AI Assistant
日期: 2024-12-22
"""

import os
import sys

def test_legacy_only_mode():
    """测试legacy_only模式"""
    print("🧪 测试主程序中的legacy_only模式")
    print("="*80)
    
    try:
        from unified_vibration_analysis import UnifiedVibrationAnalysisSystem
        
        # 初始化系统
        print("📦 初始化系统...")
        analyzer = UnifiedVibrationAnalysisSystem(
            data_dir='./data',
            data_source_mode='legacy_only'
        )
        
        print("✅ 系统初始化成功")
        
        # 运行完整的特征提取流程
        print("\n🔄 运行完整的特征提取流程...")
        features_df = analyzer.extract_features_from_data()
        
        if features_df is not None and not features_df.empty:
            print(f"✅ 特征提取成功")
            print(f"   数据形状: {features_df.shape}")
            
            # 检查数据源
            if 'data_source' in features_df.columns:
                source_counts = features_df['data_source'].value_counts()
                print(f"   数据源分布: {dict(source_counts)}")
            
            return True
        else:
            print(f"❌ 特征提取失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_method_directly():
    """直接测试增强方法"""
    print("\n🧪 直接测试增强特征提取方法")
    print("="*80)
    
    try:
        from unified_vibration_analysis import UnifiedVibrationAnalysisSystem
        
        # 初始化系统
        analyzer = UnifiedVibrationAnalysisSystem(
            data_dir='./data',
            data_source_mode='legacy_only'
        )
        
        print("📦 系统初始化成功")
        
        # 直接调用增强方法
        print("\n🔄 直接调用增强特征提取方法...")
        features_df = analyzer.extract_features_enhanced()
        
        if features_df is not None and not features_df.empty:
            print(f"✅ 增强特征提取成功")
            print(f"   数据形状: {features_df.shape}")
            
            # 检查数据源
            if 'data_source' in features_df.columns:
                source_counts = features_df['data_source'].value_counts()
                print(f"   数据源分布: {dict(source_counts)}")
            
            return True
        else:
            print(f"❌ 增强特征提取失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_vibration_data_processor():
    """测试VibrationDataProcessor"""
    print("\n🧪 测试VibrationDataProcessor")
    print("="*80)
    
    try:
        from enhanced_feature_extractor import VibrationDataProcessor
        
        print("📦 初始化VibrationDataProcessor...")
        processor = VibrationDataProcessor(fs=1000)
        
        print("🔄 处理数据目录...")
        features_df = processor.process_directory('./data')
        
        if features_df is not None and not features_df.empty:
            print(f"✅ VibrationDataProcessor处理成功")
            print(f"   数据形状: {features_df.shape}")
            
            # 检查关键列
            key_columns = ['speed_kmh', 'load_tons', 'axle_type']
            for col in key_columns:
                if col in features_df.columns:
                    valid_count = features_df[col].notna().sum()
                    print(f"   {col}: {valid_count}/{len(features_df)} 有效值")
                else:
                    print(f"   {col}: ❌ 缺失")
            
            return True
        else:
            print(f"❌ VibrationDataProcessor处理失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_step_by_step():
    """逐步测试主程序流程"""
    print("\n🧪 逐步测试主程序流程")
    print("="*80)
    
    try:
        from unified_vibration_analysis import UnifiedVibrationAnalysisSystem
        
        # 初始化系统
        analyzer = UnifiedVibrationAnalysisSystem(
            data_dir='./data',
            data_source_mode='legacy_only'
        )
        
        print("✅ 系统初始化成功")
        
        # 检查数据目录
        print(f"\n📁 检查数据目录: {analyzer.data_dir}")
        if os.path.exists(analyzer.data_dir):
            print("✅ 数据目录存在")
        else:
            print("❌ 数据目录不存在")
            return False
        
        # 检查模式设置
        print(f"\n🎯 检查模式设置: {analyzer.data_source_mode}")
        if analyzer.data_source_mode == 'legacy_only':
            print("✅ 模式设置正确")
        else:
            print("❌ 模式设置错误")
            return False
        
        # 测试增强特征提取的各个步骤
        print(f"\n🔄 测试增强特征提取的各个步骤...")
        
        # 初始化结果存储
        all_features_list = []
        processing_summary = {
            'legacy_format_samples': 0,
            'new_format_samples': 0,
            'total_samples': 0,
            'processing_errors': [],
            'data_source_mode': analyzer.data_source_mode
        }
        
        print(f"   初始化完成")
        
        # 步骤1：跳过新格式数据处理（legacy_only模式）
        print(f"\n   📊 步骤1：跳过新格式数据处理")
        print(f"      💡 当前模式 '{analyzer.data_source_mode}' 不处理新格式数据")
        
        # 步骤2：处理传统格式数据
        print(f"\n   📊 步骤2：处理传统格式数据")
        
        if processing_summary['legacy_format_samples'] > 0:
            print(f"      ✅ 传统格式数据已从扩展文件中获取: {processing_summary['legacy_format_samples']} 样本")
        else:
            print("      🔧 需要单独处理传统格式数据...")
            try:
                from enhanced_feature_extractor import VibrationDataProcessor
                print("      🔧 使用增强特征提取器处理传统格式数据...")
                
                processor = VibrationDataProcessor(fs=1000)
                legacy_features_df = processor.process_directory(analyzer.data_dir)
                
                if legacy_features_df is not None and not legacy_features_df.empty:
                    print(f"      ✅ 传统格式数据处理完成: {legacy_features_df.shape}")
                    
                    # 添加数据源标识
                    legacy_features_df['data_source'] = 'legacy_format'
                    all_features_list.append(legacy_features_df)
                    processing_summary['legacy_format_samples'] = len(legacy_features_df)
                    
                    print(f"      ✅ 数据已添加到特征列表")
                else:
                    print("      ❌ 传统格式数据处理未返回有效结果")
                    return False
                    
            except ImportError as e:
                print(f"      ❌ 传统格式处理器导入失败: {str(e)}")
                return False
            except Exception as e:
                print(f"      ❌ 传统格式数据处理失败: {str(e)}")
                import traceback
                traceback.print_exc()
                return False
        
        # 步骤3：合并数据
        print(f"\n   📊 步骤3：合并所有格式的数据（模式: {analyzer.data_source_mode}）...")
        if all_features_list:
            # 合并所有数据框
            combined_features_df = analyzer._merge_multiple_dataframes(all_features_list)
            
            if combined_features_df is not None and not combined_features_df.empty:
                processing_summary['total_samples'] = len(combined_features_df)
                
                print(f"      ✅ 数据合并完成:")
                print(f"         数据源模式: {analyzer.data_source_mode}")
                print(f"         传统格式样本: {processing_summary['legacy_format_samples']}")
                print(f"         新格式样本: {processing_summary['new_format_samples']}")
                print(f"         总样本数: {processing_summary['total_samples']}")
                print(f"         总特征数: {combined_features_df.shape[1]}")
                
                # 验证数据源分布
                if 'data_source' in combined_features_df.columns:
                    source_counts = combined_features_df['data_source'].value_counts()
                    print(f"      📊 实际数据源分布:")
                    for source, count in source_counts.items():
                        print(f"         {source}: {count} 样本 ({count/len(combined_features_df)*100:.1f}%)")
                
                # 保存合并后的特征文件
                combined_features_df.to_csv('combined_features.csv', index=False)
                print(f"      ✅ 已保存合并特征文件: combined_features.csv")
                
                return True
            else:
                print("      ❌ 数据合并失败")
                return False
        else:
            print(f"      ❌ 没有可用的特征数据（模式: {analyzer.data_source_mode}）")
            return False
            
    except Exception as e:
        print(f"❌ 逐步测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 启动legacy_only模式主程序测试")
    
    # 执行测试
    results = {}
    
    # 测试1：主程序完整流程
    results['主程序完整流程'] = test_legacy_only_mode()
    
    # 测试2：直接测试增强方法
    results['增强方法直接测试'] = test_enhanced_method_directly()
    
    # 测试3：VibrationDataProcessor
    results['VibrationDataProcessor'] = test_vibration_data_processor()
    
    # 测试4：逐步测试
    results['逐步测试'] = test_step_by_step()
    
    # 生成测试报告
    print(f"\n" + "="*80)
    print(f"📋 测试报告")
    print("="*80)
    
    passed_tests = sum(results.values())
    total_tests = len(results)
    
    print(f"\n📊 测试结果:")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n📈 总体结果:")
    print(f"   通过测试: {passed_tests}/{total_tests}")
    print(f"   通过率: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests < total_tests:
        print(f"\n🔍 问题分析:")
        print(f"   主程序中的legacy_only模式存在问题")
        print(f"   需要检查增强特征提取方法的实现")
    else:
        print(f"\n🎉 所有测试通过！")
        print(f"   legacy_only模式应该可以正常工作")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
